---
type: "agent_requested"
description: "Example description"
---

## Constants Organization

### Constants Structure

```dart
class FeatureConstants {
  FeatureConstants._(); // Private constructor

  // Group constants by category
  static const String constantName = 'value';
  static const int numericConstant = 42;

  // Use descriptive comments for complex constants
  /// Maximum length for user input validation
  static const int maxInputLength = 50;
}
```

### Constants Rules

- Use private constructor to prevent instantiation
- Group related constants together
- Use descriptive names and comments
- Separate by functional areas (validation, UI, strings)
