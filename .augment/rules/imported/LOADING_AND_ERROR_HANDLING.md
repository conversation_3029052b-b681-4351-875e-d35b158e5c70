---
type: "agent_requested"
description: "Example description"
---

## Loading and Error Handling

### Loading Management

Use `runSafe()` for all operations which can throw an error, async or sync.  
All parameters except the action function are optional.  
If `tag` is provided, loading state is tracked for that tag.  
If no `tag` is provided, a generic loading state is tracked.

```dart
// Basic usage - only required parameter
Future<void> performBasicAction() async {
  await runSafe(() async {
    final response = await api.getData();
    // Process response
  });
}

// Advanced usage - with optional parameters (use only when needed)
Future<void> performAdvancedAction() async {
  await runSafe(
    () async {
      final response = await api.updateData(data);
      if (response != null) {
        state.updateData(response);
      }
    },
    tag: 'updateData',           // Optional: for specific loading tracking
    errorMessage: '<PERSON><PERSON><PERSON><PERSON><PERSON> başarısız', // Optional: custom error message
    onError: () => _handleUpdateError(),   // Optional: custom error handling
    onSuccess: () => _showSuccessMessage(), // Optional: success callback
    onFinally: () => _cleanup(),           // Optional: cleanup callback
  );
}

// Check loading state
bool get isUpdating => isLoading('updateData'); // Check for specific tag
bool get isAnyLoading => isLoading(); // Check any loading state
```

### Error Handling

- Always use `runSafe()` for all kinds of operation which can throw an error
- Errors are handled by `runSafe()` automatically
- If manual error handling is needed, use `BaseController.handleError()` for consistent error display and consider using `onError` callback in `runSafe()`
- Provide custom error messages when appropriate
- Handle errors gracefully without crashing the app
