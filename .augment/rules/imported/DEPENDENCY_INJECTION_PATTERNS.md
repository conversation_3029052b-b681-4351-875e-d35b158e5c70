---
type: "agent_requested"
description: "Example description"
---

## Dependency Injection Patterns

### Tag-Based Route Bindings

Use tag-based bindings for features that support multiple instances (e.g., different user profiles):

```dart
class ProfileBindings implements Bindings {
  final String userId;
  final bool permanent;

  ProfileBindings({required this.userId, this.permanent = false});

  @override
  void dependencies() {
    final service = Get.find<AuthService>();
    final sharedState = Get.put(ProfileSharedState(userId), tag: userId, permanent: permanent);

    // Register all feature controllers with the same tag
    Get.lazyPut<ProfileUserInfoController>(
      () => ProfileUserInfoController(service, sharedState),
      tag: userId,
      fenix: true
    );
    Get.lazyPut<ProfileContentController>(
      () => ProfileContentController(service, sharedState),
      tag: userId,
      fenix: true
    );
    Get.lazyPut<ProfileFollowersController>(
      () => ProfileFollowersController(service, sharedState),
      tag: userId,
      fenix: true
    );
    // ... register other controllers
  }
}
```

### Simple Feature Bindings

For features that don't need multiple instances:

```dart
class FeatureBindings implements Bindings {
  @override
  void dependencies() {
    final service = Get.find<AuthService>();
    final sharedState = Get.put(FeatureSharedState());

    Get.lazyPut<FeatureInfoController>(
      () => FeatureInfoController(service, sharedState),
      fenix: true,
    );
    Get.lazyPut<FeatureContentController>(
      () => FeatureContentController(service, sharedState),
      fenix: true,
    );
  }
}
```

### Binding Rules

- Use `lazyPut()` for lazy initialization
- Set `fenix: true` for persistent instances that should survive route changes
- Use `tag` parameter for multi-instance features (e.g., user profiles, different contexts)
- Use `permanent` parameter to control lifecycle management
- Inject dependencies through constructor parameters
- Register SharedState first, then controllers that depend on it
- Use consistent tagging across all controllers in the same feature instance

### Tag-Based Controller Access

Access tagged controllers in widgets:

```dart
class ProfileWidget extends GetView<ProfileUserInfoController> {
  final String userId;

  const ProfileWidget({required this.userId, super.key});

  @override
  String? get tag => userId;  // Use the same tag as in bindings

  @override
  Widget build(BuildContext context) {
    return Obx(() => Text(controller.userPageInfo?.name ?? ''));
  }
}
```

### Route Configuration

Configure routes with parameterized bindings:

```dart
GetPage(
  name: '/userProfile/:id',
  page: () => UserProfile(userId: Get.parameters['id']!),
  binding: ProfileBindings(userId: Get.parameters['id']!),
)
```
