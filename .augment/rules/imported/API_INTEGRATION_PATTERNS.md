---
type: "agent_requested"
description: "Example description"
---

## API Integration Patterns

### Service Layer Integration

Controllers access APIs through their `BaseController`:

```dart
class FeatureController extends BaseController<FeatureSharedState> {
  Future<void> fetchData() async {
    await runSafe(loadingTag: 'fetchData', () async {
      final response = await featureApi.getData();
      if (response != null) {
        // Process response
      }
    });
  }
}
```

### API Response Handling

- Always check for null responses
- Use `runSafe()` for API calls
- Handle errors through the base controller
- Transform API models to domain models
