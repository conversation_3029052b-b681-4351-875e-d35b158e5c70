---
type: "agent_requested"
description: "Example description"
---

## Architecture Principles

### 1. Feature-Based Architecture

- **Structure**: Each feature is organized under `lib/features/{feature_name}/`
- **Isolation**: Features are self-contained with minimal cross-feature dependencies
- **Modularity**: Each feature can be developed, tested, and maintained independently
- **Scalability**: New features can be added without affecting existing ones

### 2. Layer Separation

- **Presentation Layer**: Views and Widgets (UI components)
- **Business Logic Layer**: Controllers and State Management
- **Data Layer**: Models, API Integration, and Data Transformation
- **Core Layer**: App-wide utilities, constants, and reusable components
- **Shared Layer**: Cross-feature utilities, base classes, and domain entities
