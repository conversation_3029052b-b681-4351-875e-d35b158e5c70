---
type: "agent_requested"
description: "Example description"
---

## Widget Architecture

### Core Widget Hierarchy

The app uses a comprehensive widget system with the "Ia" prefix for all custom components:

#### Foundation Widgets (Basic Building Blocks)

**Buttons:**

- `IaRoundedButton` - Primary action buttons
- `IaTextButton` - Text-only buttons
- `IaIconButton` - Icon-based buttons
- `IaCircularButton` - Circular icon buttons
- `IaFloatingActionButton` - Floating action buttons
- `IaBlurredButton` - Buttons with blur effects

**Containers:**

- `IaRoundedContainer` - Rounded corner containers
- `IaImageContainer` - Image display containers

**Inputs:**

- `IaSearchBar` - Search input fields
- `IaSlider` - Range sliders

**Indicators:**

- `IaLoadingIndicator` - Loading spinners with timeout
- `IaSearchPlaceholder` - Search state management

**Graphics:**

- `IaSvgIcon` - SVG icon display
- `IaDivider` - Custom dividers

#### Layout Widgets (Structural Components)

**Scaffolds:**

- `IaScaffold.main()` - Standard page layout
- `IaScaffold.auth()` - Authentication page layout
- `IaScaffold.search()` - Search page layout

**Navigation:**

- `IaTopBar` - Custom app bars
- `IaBottomNavigationBar` - Bottom navigation

**Panels:**

- `IaSlidingPanel` - Sliding overlay panels
- `IaBottomPanel` - Bottom sheet panels

**Screens:**

- `IaSearchScreen` - Complete search screen layout

#### Composite Widgets (Complex Components)

**Buttons (Feature-Specific):**

- `CameraButtons` - Camera-related actions
- `HobbyButtons` - Hobby selection buttons
- `HomeButtons` - Home screen actions
- `NavigationButtons` - Navigation controls
- `ProfileButtons` - Profile actions
- `SharedButtons` - Cross-feature buttons
- `VibeButtons` - Vibe-related actions

**Tiles:**

- `IaBasicInfoTile` - Basic information display
- `IaIventTile` - iVent item display
- `IaLinkedAvatars` - Connected user avatars
- `IaListTile` - Custom list items

**Feedback:**

- `CustomSnackbar` - App-wide notifications

#### Specialized Widgets (Domain-Specific)

**iVent Components:**

- `IaIventBox` - iVent card display
- `IaIventGrid` - iVent grid layout
- `IaIventThumbnail` - iVent preview images
- `IventCreateButtons` - iVent creation actions
- `IventDetailButtons` - iVent detail actions

### Widget Usage Patterns

#### IaLoadingIndicator Usage

```dart
// Basic loading indicator
const IaLoadingIndicator()

// Loading with custom text color
const IaLoadingIndicator(loadingTextColor: AppColors.white)

// Pre-configured white loading indicator
const IaLoadingIndicator.white

// Loading with timeout handling
IaLoadingIndicator(
  timeout: const Duration(seconds: 15),
  timeoutWidget: Text('İşlem zaman aşımına uğradı'),
)
```

#### IaSearchScreen Usage

```dart
IaSearchScreen(
  textEditingController: _searchController,
  searchBarLabelText: 'Kullanıcı ara',
  body: Obx(() {
    return IaSearchPlaceholder(
      entityName: 'Kullanıcı',
      isSearching: controller.isLoading,
      isQueryEmpty: controller.isQueryEmpty,
      isResultsEmpty: controller.isResultsEmpty,
      initialSearchBehavior: InitialSearchBehavior.MUST_SEARCH,
      builder: (context) => ListView.builder(
        itemCount: controller.searchResults.length,
        itemBuilder: (context, index) => IaListTile(
          title: controller.searchResults[index].name,
          onTap: () => controller.selectUser(controller.searchResults[index]),
        ),
      ),
    );
  }),
)
```

#### IaSearchPlaceholder Usage

```dart
IaSearchPlaceholder(
  entityName: 'İvent',
  iconPath: 'assets/icons/search_icon.svg',
  isSearching: controller.isLoading,
  isQueryEmpty: controller.isQueryEmpty,
  isResultsEmpty: controller.isResultsEmpty,
  initialSearchBehavior: InitialSearchBehavior.LOADED,
  builder: (context) => _buildSearchResults(),
)
```

### Widget Creation Guidelines

#### When to Create Core Widgets vs Feature Widgets

**Create Core Widgets (`lib/core/widgets/`) when:**

- The widget will be used across multiple features
- It represents a fundamental UI pattern (buttons, inputs, containers)
- It implements app-wide design system components
- It provides consistent styling and behavior

**Create Feature Widgets (`lib/features/{feature}/widgets/`) when:**

- The widget is specific to one feature's domain logic
- It contains feature-specific business logic or state
- It's unlikely to be reused outside the feature
- It represents feature-specific UI patterns

#### Widget Structure Template

```dart
class IaCustomWidget extends StatelessWidget {
  // Required parameters first
  final String requiredParam;
  final VoidCallback onAction;

  // Optional parameters with defaults
  final String? optionalParam;
  final Color backgroundColor;
  final EdgeInsetsGeometry margin;

  const IaCustomWidget({
    super.key,
    required this.requiredParam,
    required this.onAction,
    this.optionalParam,
    this.backgroundColor = AppColors.primary,
    this.margin = const EdgeInsets.all(AppDimensions.padding16),
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onAction,
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          child: Padding(
            padding: const EdgeInsets.all(AppDimensions.padding16),
            child: Text(
              optionalParam ?? requiredParam,
              style: AppTextStyles.size16Bold.copyWith(color: AppColors.white),
            ),
          ),
        ),
      ),
    );
  }
}
```

#### Widget Best Practices

**Naming:**

- Always use "Ia" prefix for core widgets
- Use descriptive names that indicate functionality
- Follow PascalCase convention

**Parameters:**

- Required parameters first, optional parameters last
- Use meaningful default values from app constants
- Provide comprehensive documentation for complex widgets

**Styling:**

- Use app constants for colors, dimensions, and text styles
- Implement consistent theming across all widgets
- Support customization through parameters when needed

**Performance:**

- Use `const` constructors when possible
- Avoid rebuilding expensive widgets unnecessarily
- Implement efficient list rendering for large datasets
