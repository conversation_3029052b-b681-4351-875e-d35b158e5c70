---
type: "agent_requested"
description: "Example description"
---

## Model Classes

### Model Structure

```dart
class ModelItem {
  final String requiredField;
  final String? optionalField;

  ModelItem({
    required this.requiredField,
    this.optionalField,
  });

  factory ModelItem.fromApiModel(ApiModel apiModel) {
    return ModelItem(
      requiredField: apiModel.field,
      optionalField: apiModel.optionalField,
    );
  }

  @override
  String toString() {
    return 'ModelItem(requiredField: $requiredField, optionalField: $optionalField)';
  }
}
```

### Model Rules

- Use immutable classes with final fields
- Provide factory constructors for API conversion
- Include `toString()` method for debugging
- Use descriptive field names
