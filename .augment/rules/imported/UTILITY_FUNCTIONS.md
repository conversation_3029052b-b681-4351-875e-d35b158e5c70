---
type: "agent_requested"
description: "Example description"
---

## Utility Functions

### Feature-Specific Utils

Place utility functions in `lib/features/{feature}/utils/`:

```dart
/// Removes duplicate items from a list based on ID
List<T> removeDuplicateItems<T extends HasId>(
  List<T> items, {
  List<T> exclude = const [],
}) {
  // Implementation
}
```

### Utility Rules

- Create pure functions without side effects
- Use generic types when applicable
- Provide comprehensive documentation
- Include parameter validation
