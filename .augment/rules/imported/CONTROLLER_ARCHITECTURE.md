---
type: "agent_requested"
description: "Example description"
---

## Controller Architecture

### Base Controller Pattern

All controllers must extend `BaseController<T extends SharedState>` for consistent behavior and shared functionality:

```dart
class FeatureSpecificController extends BaseController<FeatureSharedState> {
  // Reactive state specific to this controller
  final _items = <ItemType>[].obs;
  final _selectedItem = Rxn<ItemType>();

  // Constructor - always pass AuthService and SharedState
  FeatureSpecificController(AuthService authService, FeatureSharedState state)
    : super(authService, state);

  // Getters and setters for reactive state
  List<ItemType> get items => _items;
  set items(List<ItemType> value) => _items.assignAll(value);

  ItemType? get selectedItem => _selectedItem.value;
  set selectedItem(ItemType? value) => _selectedItem.value = value;

  @override
  Future<void> initController() async {
    super.initController();
    await _loadInitialData();
  }

  // Business logic methods
  Future<void> _loadInitialData() async {
    await runSafe(tag: 'loadItems', () async {
      final response = await featureApi.getItems();
      if (response != null) {
        items = response.map((item) => ItemType.fromApi(item)).toList();
      }
    });
  }

  Future<void> performAction() async {
    await runSafe(tag: 'performAction', () async {
      // Perform specific business logic
    });
  }
}
```

### Search Controller Pattern

For controllers that need search functionality, extend `BaseControllerWithSearch<T extends SharedState>`:

```dart
class FeatureSearchController extends BaseControllerWithSearch<FeatureSharedState> {
  final _searchResults = Rxn<SearchResultType>();

  FeatureSearchController(AuthService authService, FeatureSharedState state)
    : super(authService, state);

  SearchResultType? get searchResults => _searchResults.value;

  @override
  bool get isResultsEmpty => searchResults?.items.isEmpty ?? true;

  @override
  InitialSearchBehavior get initialSearchBehavior => InitialSearchBehavior.loaded;

  @override
  Future<void> onSearch([String? query]) async {
    // No need for runSafe as it's handled by BaseControllerWithSearch
    _searchResults.value = await featureApi.search(
      state.userId,
      q: query,
    );
  }

  // Additional business logic methods
  void selectSearchResult(SearchItem item) {
    // Handle search result selection
  }
}
```

### Controller Architecture Principles

1. **Individual Controllers**: Each controller handles a specific responsibility within a feature
2. **Shared State**: Manages reactive state accessible across all controllers in the feature
3. **Base Controller**: Provides common functionality (loading, error handling, lifecycle, API access)
4. **Tag-Based Registration**: Controllers are registered with tags for multi-instance support

### Controller Management Rules

- Each controller extends `BaseController<FeatureSharedState>` or `BaseControllerWithSearch<FeatureSharedState>`
- Controllers are registered individually in bindings with appropriate tags
- Use `runSafe()` method for error handling and loading state management
- Access APIs through inherited properties from BaseController (e.g., `usersApi`, `iventsApi`)
- Implement `initController()` for initialization logic, `closeController()` for cleanup
- Use reactive variables (`.obs`, `Rxn`, `RxBool`, etc.) for state management

### Controller Communication

Controllers communicate through:

- **Shared State**: Common reactive state accessible to all controllers
- **Direct Method Calls**: When controllers need to interact directly
- **Event-Based**: Using GetX reactive programming patterns

### Example Controller Structure

```dart
// Feature has multiple independent controllers
class ProfileUserInfoController extends BaseController<ProfileSharedState> { }
class ProfileContentController extends BaseController<ProfileSharedState> { }
class ProfileFollowersController extends BaseControllerWithSearch<ProfileSharedState> { }
class ProfileSocialController extends BaseController<ProfileSharedState> { }
```
