---
type: "agent_requested"
description: "Example description"
---

## Naming Conventions

### File Naming

- **Snake Case**: All file names use snake_case
- **Descriptive**: Names clearly indicate purpose
- **Suffixes**: Use appropriate suffixes for clarity
  - `_controller.dart` for controllers
  - `_page.dart` for pages
  - `_widget.dart` for widgets
  - `_model.dart` for models
  - `_constants.dart` for constants

### Class Naming

- **PascalCase**: All class names use PascalCase
- **Descriptive**: Names clearly indicate functionality
- **Suffixes**: Use consistent suffixes
  - `Controller` for business logic controllers
  - `Page` for screen/page widgets
  - `Widget` for custom widgets
  - `State` for state management classes
  - `Item` for model classes

### Variable and Method Naming

- **camelCase**: All variables and methods use camelCase
- **Private Members**: Prefix with underscore `_`
- **Boolean Variables**: Use `is`, `has`, `can` prefixes
- **Getters/Setters**: Use descriptive names without `get`/`set` prefixes
