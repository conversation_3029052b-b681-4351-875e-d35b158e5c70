---
type: "agent_requested"
description: "Example description"
---

## Security Considerations

### Data Protection

- Validate all user inputs
- Sanitize data before API calls
- Handle sensitive data appropriately
- Use secure storage for credentials

### Error Information

- Don't expose sensitive information in errors
- Log errors securely for debugging
- Provide user-friendly error messages
- Handle authentication failures gracefully
