---
type: "agent_requested"
description: "Example description"
---

## Directory Structure Standards

### Feature Directory Organization

```
lib/features/{feature_name}/
├── constants/          # Feature-specific constants
├── controllers/        # Business logic controllers
├── models/             # Data models and entities
├── views/              # UI screens/pages
├── utils/              # Feature-specific utilities (optional)
└── widgets/            # Feature-specific widgets
    ├── common/         # Shared widgets within feature
    └── {view_name}/    # View-specific widgets
```

### Core Directory Structure

```
lib/core/
├── cache/              # Caching mechanisms
├── constants/          # App-wide constants
├── error/              # Error handling
├── services/           # Core services (auth, etc.)
├── utils/              # Utility functions
└── widgets/            # Reusable UI components
    ├── composite/      # Complex composed widgets
    ├── foundation/     # Basic UI elements
    └── layout/         # Layout components
```
