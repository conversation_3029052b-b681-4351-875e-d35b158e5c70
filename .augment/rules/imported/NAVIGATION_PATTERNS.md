---
type: "agent_requested"
description: "Example description"
---

## Navigation Patterns

### Route Definition

```dart
abstract class FeatureRoutes {
  FeatureRoutes._();

  static const featurePage = '/featurePage';
  static const detailPage = '/detailPage';

  static final routes = [
    GetPage(
      name: featurePage,
      page: () => const FeaturePage(),
      binding: FeatureBindings(),
    ),
    GetPage(
      name: detailPage,
      page: () => const DetailPage(),
    ),
  ];
}
```

### Navigation Methods

- Use descriptive method names: `goToFeaturePage()`
- Handle navigation in controllers, not widgets
- Use `Get.toNamed()` for navigation within the app
- Use `BaseController.runSafe()` for all navigations that needs error handling
- Pass parameters through routes using GetX parameters when needed
