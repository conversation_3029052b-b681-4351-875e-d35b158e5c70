#!/usr/bin/env python3
"""
Markdown File Splitter

This script splits a markdown file into multiple separate files based on 
second-level headers (##). Each section becomes its own file with proper
naming conventions and formatting preservation.

Usage:
    python markdown_splitter.py [input_file] [output_directory]
    
If no arguments are provided, the script will prompt for input.
"""

import os
import re
import sys
import argparse
from pathlib import Path
from typing import List, Tuple, Optional


def sanitize_filename(text: str) -> str:
    """
    Convert header text to UPPER_SNAKE_CASE format suitable for filenames.
    
    Args:
        text: The header text to convert
        
    Returns:
        Sanitized filename in UPPER_SNAKE_CASE format
    """
    # Remove the ## prefix and any leading/trailing whitespace
    clean_text = text.replace('#', '').strip()
    
    # Replace special characters and spaces with underscores
    # Keep only alphanumeric characters and underscores
    sanitized = re.sub(r'[^a-zA-Z0-9\s]', '_', clean_text)
    
    # Replace multiple spaces/underscores with single underscore
    sanitized = re.sub(r'[\s_]+', '_', sanitized)
    
    # Remove leading/trailing underscores
    sanitized = sanitized.strip('_')
    
    # Convert to uppercase
    return sanitized.upper()


def parse_markdown(content: str) -> List[Tuple[str, str]]:
    """
    Parse markdown content and split it into sections based on ## headers.
    
    Args:
        content: The markdown content to parse
        
    Returns:
        List of tuples containing (header_text, section_content)
    """
    sections = []
    lines = content.split('\n')
    current_section_lines = []
    current_header = None
    
    for line in lines:
        # Check if this line is a second-level header
        if line.strip().startswith('## '):
            # Save the previous section if it exists
            if current_header is not None:
                section_content = '\n'.join(current_section_lines)
                sections.append((current_header, section_content))
            elif current_section_lines:
                # Content before first ## header
                intro_content = '\n'.join(current_section_lines)
                sections.append(('INTRO', intro_content))
            
            # Start new section
            current_header = line.strip()
            current_section_lines = [line]  # Include the header in the section
        else:
            current_section_lines.append(line)
    
    # Don't forget the last section
    if current_header is not None:
        section_content = '\n'.join(current_section_lines)
        sections.append((current_header, section_content))
    elif current_section_lines:
        # Handle case where file has no ## headers
        intro_content = '\n'.join(current_section_lines)
        sections.append(('INTRO', intro_content))
    
    return sections


def write_section_file(header: str, content: str, output_dir: Path) -> str:
    """
    Write a section to its own markdown file.
    
    Args:
        header: The header text (or 'INTRO' for content before first header)
        content: The section content including the header
        output_dir: Directory to write the file to
        
    Returns:
        The path of the created file
    """
    if header == 'INTRO':
        filename = 'INTRO.md'
    else:
        filename = f"{sanitize_filename(header)}.md"
    
    file_path = output_dir / filename
    
    # Ensure the content ends with a newline
    if content and not content.endswith('\n'):
        content += '\n'
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    return str(file_path)


def split_markdown_file(input_file: Path, output_dir: Optional[Path] = None) -> List[str]:
    """
    Split a markdown file into multiple files based on ## headers.
    
    Args:
        input_file: Path to the input markdown file
        output_dir: Directory to write output files (defaults to input file directory)
        
    Returns:
        List of created file paths
    """
    if not input_file.exists():
        raise FileNotFoundError(f"Input file not found: {input_file}")
    
    if output_dir is None:
        output_dir = input_file.parent
    else:
        output_dir.mkdir(parents=True, exist_ok=True)
    
    # Read the input file
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
    except UnicodeDecodeError:
        # Try with different encoding
        with open(input_file, 'r', encoding='latin-1') as f:
            content = f.read()
    
    # Parse the content into sections
    sections = parse_markdown(content)
    
    if not sections:
        print("No content found in the input file.")
        return []
    
    # Write each section to its own file
    created_files = []
    for header, section_content in sections:
        if section_content.strip():  # Only create files for non-empty sections
            file_path = write_section_file(header, section_content, output_dir)
            created_files.append(file_path)
            print(f"Created: {file_path}")
        else:
            print(f"Skipped empty section: {header}")
    
    return created_files


def get_user_input() -> Tuple[Path, Optional[Path]]:
    """
    Prompt user for input file and output directory.
    
    Returns:
        Tuple of (input_file_path, output_directory_path)
    """
    # Get input file
    while True:
        input_path = input("Enter the path to the markdown file: ").strip()
        if not input_path:
            print("Please provide a valid file path.")
            continue
        
        input_file = Path(input_path)
        if not input_file.exists():
            print(f"File not found: {input_file}")
            continue
        
        if not input_file.suffix.lower() in ['.md', '.markdown']:
            response = input("File doesn't have a markdown extension. Continue anyway? (y/n): ")
            if response.lower() not in ['y', 'yes']:
                continue
        
        break
    
    # Get output directory (optional)
    output_path = input("Enter output directory (leave empty for same directory as input): ").strip()
    output_dir = Path(output_path) if output_path else None
    
    return input_file, output_dir


def main():
    """Main function to handle command line arguments and execute the splitting."""
    parser = argparse.ArgumentParser(
        description="Split a markdown file into multiple files based on ## headers",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python markdown_splitter.py document.md
  python markdown_splitter.py document.md ./output/
  python markdown_splitter.py  # Interactive mode
        """
    )
    parser.add_argument('input_file', nargs='?', help='Path to the input markdown file')
    parser.add_argument('output_dir', nargs='?', help='Output directory (optional)')
    
    args = parser.parse_args()
    
    try:
        if args.input_file:
            input_file = Path(args.input_file)
            output_dir = Path(args.output_dir) if args.output_dir else None
        else:
            # Interactive mode
            input_file, output_dir = get_user_input()
        
        print(f"\nProcessing: {input_file}")
        if output_dir:
            print(f"Output directory: {output_dir}")
        else:
            print(f"Output directory: {input_file.parent}")
        
        print("\nSplitting markdown file...")
        created_files = split_markdown_file(input_file, output_dir)
        
        if created_files:
            print(f"\n✅ Successfully created {len(created_files)} files:")
            for file_path in created_files:
                print(f"  • {Path(file_path).name}")
        else:
            print("\n⚠️  No files were created. The input file might be empty or have no ## headers.")
    
    except KeyboardInterrupt:
        print("\n\n❌ Operation cancelled by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
