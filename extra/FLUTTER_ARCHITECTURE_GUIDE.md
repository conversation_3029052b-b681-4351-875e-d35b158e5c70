# Flutter Architecture & Coding Standards Guide

## Overview

This guide defines the architectural patterns and coding standards for the iVent Flutter application based on comprehensive analysis of the Auth and Home features. The architecture follows a feature-based modular approach with clean separation of concerns, utilizing GetX for state management and dependency injection.

## Complete Project Structure

### Root Directory Organization

```
lib/
├── api/                    # AUTO-GENERATED - OpenAPI/Swagger codegen
├── core/                   # App-wide shared components and utilities
├── features/               # Feature-based modules
├── routes/                 # Navigation and route definitions
├── shared/                 # Cross-feature shared code
└── main.dart              # Application entry point
```

### API Layer (Auto-Generated)

**⚠️ CRITICAL: The `lib/api/` directory is auto-generated by OpenAPI/Swagger codegen and must NEVER be manually modified.**

```
lib/api/
├── api/                   # Generated API client classes
├── auth/                  # Generated authentication classes
├── doc/                   # Generated API documentation
├── model/                 # Generated data models and DTOs
├── api.dart              # Main API export file
├── api_client.dart       # HTTP client implementation
├── api_exception.dart    # API exception handling
└── api_helper.dart       # API utility functions
```

**API Integration Rules:**

- Never edit files in `lib/api/` directly
- Use the generated API classes through the `AuthService` layer
- Regenerate API code when backend changes occur
- Transform API models to domain models in your controllers

### Core Directory Structure

```
lib/core/
├── cache/                 # Caching mechanisms
├── constants/             # App-wide constants (colors, dimensions, styles)
├── error/                 # Error handling utilities
├── services/              # Core services (AuthService, etc.)
├── utils/                 # Utility functions and extensions
└── widgets/               # Reusable UI components with "Ia" prefix
    ├── composite/         # Complex composed widgets
    │   ├── buttons/       # Composite button components
    │   ├── feedback/      # User feedback components
    │   └── tiles/         # List and card tiles
    ├── foundation/        # Basic UI building blocks
    │   ├── buttons/       # Basic button components
    │   ├── containers/    # Container widgets
    │   ├── dialogs/       # Dialog components
    │   ├── graphics/      # Icons, dividers, graphics
    │   ├── indicators/    # Loading, search placeholders
    │   └── inputs/        # Input fields and controls
    ├── layout/            # Layout and structural components
    │   ├── navigation/    # Navigation bars and components
    │   ├── panels/        # Panel and overlay components
    │   ├── scaffolds/     # Custom scaffold implementations
    │   └── screens/       # Screen layout templates
    └── specialized/       # Domain-specific widgets
        └── ivent/         # iVent-specific UI components
```

### Shared Directory Structure

```
lib/shared/
├── controllers/           # Base controllers and shared state
├── domain/               # Cross-feature domain entities
│   └── entities/         # Shared business entities
└── pages/                # Shared pages (error screens, etc.)
```

### Routes Directory Structure

```
lib/routes/
├── app_pages.dart        # Main route configuration
├── global_bindings.dart  # Global dependency bindings
├── {feature}.dart        # Feature-specific route definitions
└── other.dart           # Utility and error routes
```

## Architecture Principles

### 1. Feature-Based Architecture

- **Structure**: Each feature is organized under `lib/features/{feature_name}/`
- **Isolation**: Features are self-contained with minimal cross-feature dependencies
- **Modularity**: Each feature can be developed, tested, and maintained independently
- **Scalability**: New features can be added without affecting existing ones

### 2. Layer Separation

- **Presentation Layer**: Pages and Widgets (UI components)
- **Business Logic Layer**: Controllers and State Management
- **Data Layer**: Models, API Integration, and Data Transformation
- **Core Layer**: App-wide utilities, constants, and reusable components
- **Shared Layer**: Cross-feature utilities, base classes, and domain entities

## Directory Structure Standards

### Feature Directory Organization

```
lib/features/{feature_name}/
├── constants/           # Feature-specific constants
├── controllers/         # Business logic controllers
│   └── sub_controllers/ # Specialized controllers
├── models/             # Data models and DTOs
├── pages/              # UI screens/pages
├── utils/              # Feature-specific utilities (optional)
└── widgets/            # Feature-specific widgets
    ├── common/         # Shared widgets within feature
    ├── form/           # Form-related widgets
    └── {category}/     # Categorized widgets
```

### Core Directory Structure

```
lib/core/
├── cache/              # Caching mechanisms
├── constants/          # App-wide constants
├── error/              # Error handling
├── services/           # Core services (auth, etc.)
├── utils/              # Utility functions
└── widgets/            # Reusable UI components
    ├── composite/      # Complex composed widgets
    ├── foundation/     # Basic UI elements
    ├── layout/         # Layout components
    └── specialized/    # Domain-specific widgets
```

## Naming Conventions

### File Naming

- **Snake Case**: All file names use snake_case
- **Descriptive**: Names clearly indicate purpose
- **Suffixes**: Use appropriate suffixes for clarity
  - `_controller.dart` for controllers
  - `_page.dart` for pages
  - `_widget.dart` for widgets
  - `_model.dart` for models
  - `_constants.dart` for constants

### Class Naming

- **PascalCase**: All class names use PascalCase
- **Descriptive**: Names clearly indicate functionality
- **Suffixes**: Use consistent suffixes
  - `Controller` for business logic controllers
  - `Page` for screen/page widgets
  - `Widget` for custom widgets
  - `State` for state management classes
  - `Item` for model classes

### Variable and Method Naming

- **camelCase**: All variables and methods use camelCase
- **Private Members**: Prefix with underscore `_`
- **Boolean Variables**: Use `is`, `has`, `can` prefixes
- **Getters/Setters**: Use descriptive names without `get`/`set` prefixes

## Controller Architecture

### Base Controller Pattern

All controllers must extend `BaseController<T extends SharedState>` for consistent behavior and shared functionality:

```dart
class FeatureController extends BaseController<FeatureSharedState> {
  // Child controllers - declared as late final for initialization in initController
  late final SubController1 subController1;
  late final SubController2 subController2;
  late final SubController3 subController3;

  // Constructor - always pass AuthService and SharedState
  FeatureController(AuthService authService, FeatureSharedState state)
    : super(authService, state);

  @override
  Future<void> initController() async {
    super.initController();

    // Initialize child controllers in dependency order
    // Controllers with no dependencies first
    subController1 = Get.put(SubController1(authService, state));
    subController2 = Get.put(SubController2(authService, state));

    // Controllers with dependencies on other controllers last
    subController3 = Get.put(SubController3(authService, state, subController1));

    // Perform any async initialization
    await _loadInitialData();
  }

  @override
  void closeController() {
    // Clean up child controllers in reverse order
    Get.delete<SubController3>();
    Get.delete<SubController2>();
    Get.delete<SubController1>();

    // Call parent cleanup
    super.closeController();
  }

  // Navigation methods - delegate to appropriate sub-controllers
  void goToFeaturePage() => Get.toNamed(FeatureRoutes.FEATURE_PAGE);
  void goToDetailPage(String id) => Get.toNamed(FeatureRoutes.DETAIL_PAGE, arguments: id);

  // Private helper methods
  Future<void> _loadInitialData() async {
    await runWithLoading(
      () async {
        // Load any required initial data
      },
      loadingTag: 'initData',
    );
  }
}
```

### Sub-Controller Pattern

Sub-controllers handle specific responsibilities within a feature:

```dart
class SubController extends BaseController<FeatureSharedState> {
  // Dependencies from other controllers (if needed)
  final OtherController? otherController;

  // Reactive state specific to this sub-controller
  final _items = <ItemType>[].obs;
  final _selectedItem = Rxn<ItemType>();

  SubController(
    AuthService authService,
    FeatureSharedState state,
    [this.otherController]
  ) : super(authService, state);

  // Getters for reactive state
  List<ItemType> get items => _items;
  ItemType? get selectedItem => _selectedItem.value;

  // Setters for reactive state
  set items(List<ItemType> value) => _items.assignAll(value);
  set selectedItem(ItemType? value) => _selectedItem.value = value;

  @override
  Future<void> initController() async {
    super.initController();
    await loadItems();
  }

  // Business logic methods
  Future<void> loadItems() async {
    await runWithLoading(
      () async {
        final response = await featureApi.getItems();
        if (response != null) {
          items = response.map((item) => ItemType.fromApi(item)).toList();
        }
      },
      loadingTag: 'loadItems',
    );
  }

  void selectItem(ItemType item) {
    selectedItem = item;
    // Notify other controllers if needed
    otherController?.onItemSelected(item);
  }
}
```

### Controller Hierarchy

1. **Main Controller**: Coordinates feature functionality and manages navigation
2. **Sub-Controllers**: Handle specific responsibilities (data, UI state, business logic)
3. **Shared State**: Manages reactive state accessible across all controllers
4. **Base Controller**: Provides common functionality (loading, error handling, lifecycle)

### Sub-Controller Management Rules

- Initialize sub-controllers in dependency order in `initController()`
- Use `Get.put()` for registration with proper typing
- Clean up with `Get.delete<T>()` in reverse order in `closeController()`
- Pass shared dependencies (authService, state) to all sub-controllers
- Pass controller dependencies as additional constructor parameters
- Use `late final` declarations for child controllers

## State Management

### Shared State Pattern

Each feature has a dedicated shared state class:

```dart
class FeatureSharedState extends SharedState {
  // Reactive variables using GetX observables
  final _property = ''.obs;
  final _list = <Type>[].obs;
  final _nullable = Rxn<Type>();

  // Getters and setters
  Type get property => _property.value;
  set property(Type value) => _property.value = value;

  // Helper methods for state manipulation
  void clearAll() {
    // Reset state to defaults
  }
}
```

### State Management Rules

- Use GetX observables (`.obs`, `Rxn<T>()`) for reactive state
- Provide getters and setters for clean access
- Include helper methods for complex state operations
- Keep state classes focused on data, not business logic

## Loading and Error Handling

### Loading Management

Use `runWithLoading()` for async operations. All parameters except the action function are optional:

```dart
// Basic usage - only required parameter
Future<void> performBasicAction() async {
  await runWithLoading(() async {
    final response = await api.getData();
    // Process response
  });
}

// Advanced usage - with optional parameters (use only when needed)
Future<void> performAdvancedAction() async {
  await runWithLoading(
    () async {
      final response = await api.updateData(data);
      if (response != null) {
        state.updateData(response);
      }
    },
    loadingTag: 'updateData',           // Optional: for specific loading tracking
    errorMessage: 'Güncelleme başarısız', // Optional: custom error message
    onError: () => _handleUpdateError(),   // Optional: custom error handling
    onSuccess: () => _showSuccessMessage(), // Optional: success callback
    onFinally: () => _cleanup(),           // Optional: cleanup callback
  );
}

// Check loading state
bool get isUpdating => isLoading('updateData');
bool get isAnyLoading => isLoading(); // Check any loading state
```

### Error Handling

- Use `BaseController.handleError()` for consistent error display
- Provide custom error messages when appropriate
- Handle errors gracefully without crashing the app

## Page Structure

### Page Organization

Always use `IaScaffold` instead of `Scaffold` for consistent app-wide styling:

```dart
class FeaturePage extends StatefulWidget {
  const FeaturePage({super.key});

  @override
  State<FeaturePage> createState() => _FeaturePageState();
}

class _FeaturePageState extends State<FeaturePage> {
  late final FeatureController _controller;

  @override
  void initState() {
    super.initState();
    _controller = Get.find<FeatureController>();
  }

  @override
  Widget build(BuildContext context) {
    return IaScaffold.main(
      appBar: IaTopBar(
        title: 'Feature Title',
        onBackPressed: () => Get.back(),
      ),
      body: Obx(() {
        if (_controller.isLoading()) {
          return const IaLoadingIndicator();
        }

        return Column(
          children: [
            // Your UI implementation using Ia-prefixed widgets
            IaRoundedButton(
              text: 'Action Button',
              onPressed: _controller.performAction,
            ),
          ],
        );
      }),
      floatingActionButton: IaFloatingActionButton(
        onPressed: _controller.addItem,
        child: const Icon(Icons.add),
      ),
    );
  }
}
```

### Page Rules

- **Always use `IaScaffold`** instead of `Scaffold` for consistent styling
- Use `StatefulWidget` for pages with local state
- Get controllers using `Get.find<T>()` in `initState()`
- Use `Obx()` for reactive UI updates
- Prefer Ia-prefixed widgets over standard Flutter widgets
- Handle loading states with `IaLoadingIndicator`
- Dispose resources properly in `dispose()` if needed

## Widget Architecture

### Core Widget Hierarchy

The app uses a comprehensive widget system with the "Ia" prefix for all custom components:

#### Foundation Widgets (Basic Building Blocks)

**Buttons:**

- `IaRoundedButton` - Primary action buttons
- `IaTextButton` - Text-only buttons
- `IaIconButton` - Icon-based buttons
- `IaCircularButton` - Circular icon buttons
- `IaFloatingActionButton` - Floating action buttons
- `IaBlurredButton` - Buttons with blur effects

**Containers:**

- `IaRoundedContainer` - Rounded corner containers
- `IaImageContainer` - Image display containers

**Inputs:**

- `IaSearchBar` - Search input fields
- `IaSlider` - Range sliders

**Indicators:**

- `IaLoadingIndicator` - Loading spinners with timeout
- `IaSearchPlaceholder` - Search state management

**Graphics:**

- `IaSvgIcon` - SVG icon display
- `IaDivider` - Custom dividers

#### Layout Widgets (Structural Components)

**Scaffolds:**

- `IaScaffold.main()` - Standard page layout
- `IaScaffold.auth()` - Authentication page layout
- `IaScaffold.search()` - Search page layout

**Navigation:**

- `IaTopBar` - Custom app bars
- `IaBottomNavigationBar` - Bottom navigation

**Panels:**

- `IaSlidingPanel` - Sliding overlay panels
- `IaBottomPanel` - Bottom sheet panels

**Screens:**

- `IaSearchScreen` - Complete search screen layout

#### Composite Widgets (Complex Components)

**Buttons (Feature-Specific):**

- `CameraButtons` - Camera-related actions
- `HobbyButtons` - Hobby selection buttons
- `HomeButtons` - Home screen actions
- `NavigationButtons` - Navigation controls
- `ProfileButtons` - Profile actions
- `SharedButtons` - Cross-feature buttons
- `VibeButtons` - Vibe-related actions

**Tiles:**

- `IaBasicInfoTile` - Basic information display
- `IaIventTile` - iVent item display
- `IaLinkedAvatars` - Connected user avatars
- `IaListTile` - Custom list items

**Feedback:**

- `CustomSnackbar` - App-wide notifications

#### Specialized Widgets (Domain-Specific)

**iVent Components:**

- `IaIventBox` - iVent card display
- `IaIventGrid` - iVent grid layout
- `IaIventThumbnail` - iVent preview images
- `IventCreateButtons` - iVent creation actions
- `IventDetailButtons` - iVent detail actions

### Widget Usage Patterns

#### IaLoadingIndicator Usage

```dart
// Basic loading indicator
const IaLoadingIndicator()

// Loading with custom text color
const IaLoadingIndicator(loadingTextColor: AppColors.white)

// Pre-configured white loading indicator
const IaLoadingIndicator.white

// Loading with timeout handling
IaLoadingIndicator(
  timeout: const Duration(seconds: 15),
  timeoutWidget: Text('İşlem zaman aşımına uğradı'),
)
```

#### IaSearchScreen Usage

```dart
IaSearchScreen(
  textEditingController: _searchController,
  searchBarLabelText: 'Kullanıcı ara',
  body: Obx(() {
    return IaSearchPlaceholder(
      entityName: 'Kullanıcı',
      isSearching: controller.isLoading('searchUser'),
      isQueryEmpty: controller.isQueryEmpty,
      isResultsEmpty: controller.isResultsEmpty,
      initialSearchBehavior: InitialSearchBehavior.MUST_SEARCH,
      builder: (context) => ListView.builder(
        itemCount: controller.searchResults.length,
        itemBuilder: (context, index) => IaListTile(
          title: controller.searchResults[index].name,
          onTap: () => controller.selectUser(controller.searchResults[index]),
        ),
      ),
    );
  }),
)
```

#### IaSearchPlaceholder Usage

```dart
IaSearchPlaceholder(
  entityName: 'İvent',
  iconPath: 'assets/icons/search_icon.svg',
  isSearching: controller.isLoading('searchUser'),
  isQueryEmpty: controller.isQueryEmpty,
  isResultsEmpty: controller.isResultsEmpty,
  initialSearchBehavior: InitialSearchBehavior.LOADED,
  builder: (context) => _buildSearchResults(),
)
```

### Widget Creation Guidelines

#### When to Create Core Widgets vs Feature Widgets

**Create Core Widgets (`lib/core/widgets/`) when:**

- The widget will be used across multiple features
- It represents a fundamental UI pattern (buttons, inputs, containers)
- It implements app-wide design system components
- It provides consistent styling and behavior

**Create Feature Widgets (`lib/features/{feature}/widgets/`) when:**

- The widget is specific to one feature's domain logic
- It contains feature-specific business logic or state
- It's unlikely to be reused outside the feature
- It represents feature-specific UI patterns

#### Widget Structure Template

```dart
class IaCustomWidget extends StatelessWidget {
  // Required parameters first
  final String requiredParam;
  final VoidCallback onAction;

  // Optional parameters with defaults
  final String? optionalParam;
  final Color backgroundColor;
  final EdgeInsetsGeometry margin;

  const IaCustomWidget({
    super.key,
    required this.requiredParam,
    required this.onAction,
    this.optionalParam,
    this.backgroundColor = AppColors.primary,
    this.margin = const EdgeInsets.all(AppDimensions.padding16),
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onAction,
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          child: Padding(
            padding: const EdgeInsets.all(AppDimensions.padding16),
            child: Text(
              optionalParam ?? requiredParam,
              style: AppTextStyles.size16Bold.copyWith(color: AppColors.white),
            ),
          ),
        ),
      ),
    );
  }
}
```

#### Widget Best Practices

**Naming:**

- Always use "Ia" prefix for core widgets
- Use descriptive names that indicate functionality
- Follow PascalCase convention

**Parameters:**

- Required parameters first, optional parameters last
- Use meaningful default values from app constants
- Provide comprehensive documentation for complex widgets

**Styling:**

- Use app constants for colors, dimensions, and text styles
- Implement consistent theming across all widgets
- Support customization through parameters when needed

**Performance:**

- Use `const` constructors when possible
- Avoid rebuilding expensive widgets unnecessarily
- Implement efficient list rendering for large datasets

## Constants Organization

### Constants Structure

```dart
class FeatureConstants {
  FeatureConstants._(); // Private constructor

  // Group constants by category
  static const String constantName = 'value';
  static const int numericConstant = 42;

  // Use descriptive comments for complex constants
  /// Maximum length for user input validation
  static const int maxInputLength = 50;
}
```

### Constants Rules

- Use private constructor to prevent instantiation
- Group related constants together
- Use descriptive names and comments
- Separate by functional areas (validation, UI, strings)

## Model Classes

### Model Structure

```dart
class ModelItem {
  final String requiredField;
  final String? optionalField;

  ModelItem({
    required this.requiredField,
    this.optionalField,
  });

  factory ModelItem.fromApiModel(ApiModel apiModel) {
    return ModelItem(
      requiredField: apiModel.field,
      optionalField: apiModel.optionalField,
    );
  }

  @override
  String toString() {
    return 'ModelItem(requiredField: $requiredField, optionalField: $optionalField)';
  }
}
```

### Model Rules

- Use immutable classes with final fields
- Provide factory constructors for API conversion
- Include `toString()` method for debugging
- Use descriptive field names

## Import Organization

### Import Order

1. Flutter/Dart imports
2. Third-party package imports
3. App-wide imports (core, shared)
4. Feature-specific imports
5. Relative imports

### Import Example

```dart
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/shared/controllers/base_controller.dart';
import 'package:ivent_app/features/auth/models/contact.dart';
import '../constants/auth_strings.dart';
```

## Navigation Patterns

### Route Definition

```dart
abstract class FeatureRoutes {
  FeatureRoutes._();

  static const FEATURE_PAGE = '/feature_page';
  static const DETAIL_PAGE = '/detail_page';
}
```

### Navigation Methods

- Use descriptive method names: `goToFeaturePage()`
- Handle navigation in controllers, not widgets
- Pass parameters through route arguments when needed

## Code Quality Standards

### General Rules

1. **Single Responsibility**: Each class has one clear purpose
2. **Dependency Injection**: Use constructor injection for dependencies
3. **Immutability**: Prefer immutable classes and final fields
4. **Error Handling**: Handle errors gracefully with user feedback
5. **Performance**: Use lazy loading and efficient state management
6. **Testing**: Write testable code with clear separation of concerns

### Code Review Checklist

- [ ] Follows naming conventions
- [ ] Proper directory structure
- [ ] Extends appropriate base classes
- [ ] Handles errors gracefully
- [ ] Uses reactive state management correctly
- [ ] Includes proper documentation
- [ ] Follows import organization
- [ ] Implements proper resource cleanup

## Professional Code Quality Improvements

### Refactoring Common Anti-Patterns

#### Anti-Pattern: Direct API Calls in Widgets

**❌ Avoid:**

```dart
class BadWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: ApiClient().getData(), // Direct API call
      builder: (context, snapshot) => Text(snapshot.data?.toString() ?? ''),
    );
  }
}
```

**✅ Recommended:**

```dart
class GoodWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final controller = Get.find<FeatureController>();
    return Obx(() {
      if (controller.isLoading()) return const IaLoadingIndicator();
      return Text(controller.data?.toString() ?? '');
    });
  }
}
```

#### Anti-Pattern: Mixing Business Logic with UI

**❌ Avoid:**

```dart
class BadPage extends StatefulWidget {
  @override
  State<BadPage> createState() => _BadPageState();
}

class _BadPageState extends State<BadPage> {
  List<Item> items = [];
  bool isLoading = false;

  Future<void> loadItems() async {
    setState(() => isLoading = true);
    try {
      final response = await ApiClient().getItems();
      setState(() => items = response);
    } catch (e) {
      // Handle error in UI
    } finally {
      setState(() => isLoading = false);
    }
  }
}
```

**✅ Recommended:**

```dart
class GoodPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final controller = Get.find<FeatureController>();
    return IaScaffold.main(
      body: Obx(() {
        if (controller.isLoading()) return const IaLoadingIndicator();
        return ListView.builder(
          itemCount: controller.items.length,
          itemBuilder: (context, index) => IaListTile(
            title: controller.items[index].name,
          ),
        );
      }),
    );
  }
}
```

#### Anti-Pattern: Hardcoded Strings and Values

**❌ Avoid:**

```dart
Text('Kullanıcı bulunamadı', style: TextStyle(fontSize: 16))
```

**✅ Recommended:**

```dart
Text(FeatureStrings.userNotFound, style: AppTextStyles.size16Regular)
```

### Advanced Architecture Patterns

#### Repository Pattern for Data Layer

```dart
abstract class FeatureRepository {
  Future<List<Item>> getItems();
  Future<Item?> getItemById(String id);
  Future<void> createItem(CreateItemDto dto);
}

class FeatureRepositoryImpl implements FeatureRepository {
  final AuthService authService;
  final CacheManager cacheManager;

  FeatureRepositoryImpl(this.authService, this.cacheManager);

  @override
  Future<List<Item>> getItems() async {
    // Check cache first
    final cached = await cacheManager.getFromCache('items');
    if (cached != null) return cached;

    // Fetch from API
    final response = await featureApi.getItems();
    final items = response?.map((item) => Item.fromApi(item)).toList() ?? [];

    // Cache the result
    await cacheManager.putInCache('items', items);
    return items;
  }
}
```

#### Use Case Pattern for Complex Business Logic

```dart
class GetFilteredItemsUseCase {
  final FeatureRepository repository;
  final FilterService filterService;

  GetFilteredItemsUseCase(this.repository, this.filterService);

  Future<List<Item>> execute(FilterCriteria criteria) async {
    final items = await repository.getItems();
    return filterService.applyFilters(items, criteria);
  }
}
```

### Performance Optimization Strategies

#### Efficient List Rendering

```dart
class OptimizedListView extends StatelessWidget {
  final List<Item> items;

  const OptimizedListView({super.key, required this.items});

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: items.length,
      itemExtent: 80.0, // Fixed height for better performance
      cacheExtent: 1000.0, // Cache more items
      itemBuilder: (context, index) {
        final item = items[index];
        return IaListTile(
          key: ValueKey(item.id), // Stable keys for efficient updates
          title: item.name,
          subtitle: item.description,
        );
      },
    );
  }
}
```

#### Memory Management Best Practices

```dart
class MemoryEfficientController extends BaseController<FeatureSharedState> {
  StreamSubscription? _subscription;
  Timer? _timer;

  @override
  Future<void> initController() async {
    super.initController();
    _setupPeriodicUpdates();
  }

  @override
  void closeController() {
    // Clean up resources to prevent memory leaks
    _subscription?.cancel();
    _timer?.cancel();
    super.closeController();
  }

  void _setupPeriodicUpdates() {
    _timer = Timer.periodic(const Duration(minutes: 5), (_) {
      if (!isLoading()) {
        refreshData();
      }
    });
  }
}
```

## API Integration Patterns

### Service Layer Integration

Controllers access APIs through the `AuthService`:

```dart
class FeatureController extends BaseController<FeatureSharedState> {
  Future<void> fetchData() async {
    await runWithLoading(
      () async {
        final response = await featureApi.getData();
        if (response != null) {
          // Process response
        }
      },
      loadingTag: 'fetchData',
    );
  }
}
```

### API Response Handling

- Always check for null responses
- Use `runWithLoading()` for API calls
- Handle errors through the base controller
- Transform API models to domain models

## Validation Patterns

### Form Validation

```dart
class ValidationController extends BaseController<FeatureSharedState> {
  bool get isFormValid => _validateAllFields();

  bool _validateAllFields() {
    return _validateField1() && _validateField2();
  }

  bool _validateField1() {
    return state.field1.length >= ValidationConstants.minLength;
  }
}
```

### Validation Rules

- Create validation constants for reusable rules
- Implement validation in controllers, not widgets
- Provide real-time validation feedback
- Use descriptive validation error messages

## Utility Functions

### Feature-Specific Utils

Place utility functions in `lib/features/{feature}/utils/`:

```dart
/// Removes duplicate items from a list based on ID
List<T> removeDuplicateItems<T extends HasId>(
  List<T> items, {
  List<T> exclude = const [],
}) {
  // Implementation
}
```

### Utility Rules

- Create pure functions without side effects
- Use generic types when applicable
- Provide comprehensive documentation
- Include parameter validation

## Dependency Injection Patterns

### Route Bindings

```dart
class FeatureBindings implements Bindings {
  @override
  void dependencies() {
    if (Get.isRegistered<FeatureController>()) return;

    Get.lazyPut(() => FeatureSharedState(), fenix: true);
    Get.lazyPut<FeatureController>(
      () => FeatureController(
        Get.find<AuthService>(),
        Get.find<FeatureSharedState>(),
      ),
      fenix: true,
    );
  }
}
```

### Binding Rules

- Use `lazyPut()` for lazy initialization
- Set `fenix: true` for persistent instances
- Check registration to avoid duplicates
- Inject dependencies through constructor

## Search Functionality Patterns

### Search Controller Extension

For features with search functionality:

```dart
class SearchController extends BaseControllerWithSearch<FeatureSharedState> {
  final _searchResults = <ResultType>[].obs;

  @override
  bool get isResultsEmpty => _searchResults.isEmpty;

  @override
  Future<void> onSearch([String? query]) async {
    await runWithLoading(
      () async {
        final results = await api.search(query);
        _searchResults.assignAll(results ?? []);
      },
      loadingTag: 'search',
    );
  }
}
```

## Performance Optimization

### Lazy Loading

- Use `Get.lazyPut()` for controller registration
- Implement pagination for large data sets
- Load data on-demand in `initController()`

### Memory Management

- Dispose controllers in `closeController()`
- Clear large collections when not needed
- Use weak references for temporary data

### UI Performance

- Use `Obx()` for minimal reactive rebuilds
- Avoid unnecessary widget rebuilds
- Implement efficient list rendering

## Testing Considerations

### Testable Architecture

- Inject dependencies through constructors
- Separate business logic from UI
- Use pure functions for utilities
- Mock external dependencies

### Test Structure

```dart
void main() {
  group('FeatureController', () {
    late FeatureController controller;
    late MockAuthService mockAuthService;
    late FeatureSharedState state;

    setUp(() {
      mockAuthService = MockAuthService();
      state = FeatureSharedState();
      controller = FeatureController(mockAuthService, state);
    });

    test('should perform expected behavior', () {
      // Test implementation
    });
  });
}
```

## Security Considerations

### Data Protection

- Validate all user inputs
- Sanitize data before API calls
- Handle sensitive data appropriately
- Use secure storage for credentials

### Error Information

- Don't expose sensitive information in errors
- Log errors securely for debugging
- Provide user-friendly error messages
- Handle authentication failures gracefully

## Localization Patterns

### String Constants

Organize strings by feature and category:

```dart
class FeatureStrings {
  FeatureStrings._();

  // Button texts
  static const String continueButton = 'Devam Et';
  static const String cancelButton = 'İptal';

  // Error messages
  static const String validationError = 'Girdiğiniz bilgi geçersiz';

  // Informational messages
  static const String welcomeMessage = 'Hoş geldiniz';
}
```

## Migration and Versioning

### Feature Evolution

- Maintain backward compatibility when possible
- Use feature flags for gradual rollouts
- Document breaking changes clearly
- Provide migration guides for major changes

### API Versioning

- Handle API version changes gracefully
- Implement fallback mechanisms
- Test with multiple API versions
- Document API dependencies

This comprehensive guide ensures consistent, maintainable, and scalable Flutter code across all features in the iVent application, promoting best practices and architectural excellence.
