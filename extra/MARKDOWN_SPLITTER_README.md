# Markdown File Splitter

A Python script that splits a markdown file into multiple separate files based on second-level headers (`##`).

## Features

- ✅ Splits markdown files at each `##` header
- ✅ Creates separate files with UPPER_SNAKE_CASE naming
- ✅ Preserves original markdown formatting
- ✅ Handles content before first header (saves as `INTRO.md`)
- ✅ Sanitizes special characters in filenames
- ✅ Skips empty sections
- ✅ Supports custom output directories
- ✅ Interactive and command-line modes
- ✅ Robust error handling

## Usage

### Command Line Mode

```bash
# Split file in same directory
python markdown_splitter.py document.md

# Split file to specific output directory
python markdown_splitter.py document.md ./output/

# Help information
python markdown_splitter.py --help
```

### Interactive Mode

```bash
# Run without arguments for interactive prompts
python markdown_splitter.py
```

## Examples

### Input File: `example.md`

```markdown
# My Document

Introduction content here.

## Getting Started

Getting started instructions.

## API Reference

API documentation here.

## Troubleshooting

Common issues and solutions.
```

### Output Files Created:

- `INTRO.md` - Contains title and introduction
- `GETTING_STARTED.md` - Contains the getting started section
- `API_REFERENCE.md` - Contains the API reference section
- `TROUBLESHOOTING.md` - Contains the troubleshooting section

## Header Name Conversion

The script converts header text to valid filenames using these rules:

| Original Header            | Output Filename          |
| -------------------------- | ------------------------ |
| `## Getting Started`       | `GETTING_STARTED.md`     |
| `## API Reference`         | `API_REFERENCE.md`       |
| `## Configuration & Setup` | `CONFIGURATION_SETUP.md` |
| `## Special Characters!@#` | `SPECIAL_CHARACTERS.md`  |
| `## Multiple   Spaces`     | `MULTIPLE_SPACES.md`     |

## Requirements

- Python 3.6 or higher
- No external dependencies (uses only standard library)

## Error Handling

The script handles various edge cases:

- Missing input files
- Non-markdown files (with confirmation prompt)
- Unicode encoding issues
- Empty sections (automatically skipped)
- Special characters in headers
- Content before first `##` header
- Empty headers (`## ` without text)

## License

This script is provided as-is for educational and practical use.
