{"version": "0.2.0", "configurations": [{"name": "iOS Simulator", "request": "launch", "type": "dart", "program": "lib/main.dart", "deviceId": "9833FB58-940A-4D00-A182-C57E2D2C740D", "flutterMode": "debug"}, {"name": "Android Device", "request": "launch", "type": "dart", "program": "lib/main.dart", "deviceId": "emulator-5554", "flutterMode": "debug"}, {"name": "Android Device (Release)", "request": "launch", "type": "dart", "program": "lib/main.dart", "deviceId": "emulator-5554", "flutterMode": "release"}, {"name": "Chrome", "request": "launch", "type": "dart", "program": "lib/main.dart", "deviceId": "chrome", "flutterMode": "debug"}, {"name": "Umut 11T", "request": "launch", "type": "dart", "program": "lib/main.dart", "deviceId": "zxfion7h9tuo79ij", "flutterMode": "debug"}, {"name": "Umut 11T Wireless", "request": "launch", "type": "dart", "program": "lib/main.dart", "deviceId": "192.168.1.8:42883", "flutterMode": "debug"}]}