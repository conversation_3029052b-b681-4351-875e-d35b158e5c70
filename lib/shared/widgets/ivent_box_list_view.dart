import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_icon_button.dart';
import 'package:ivent_app/core/widgets/foundation/containers/ia_rounded_container.dart';
import 'package:ivent_app/features/ivent_detail/ivent_detail_pages.dart';
import 'package:ivent_app/shared/widgets/buttons/ivent_favorite_button.dart';
import 'package:ivent_app/shared/widgets/buttons/ivent_share_button.dart';
import 'package:ivent_app/shared/widgets/ivent_thumbnail.dart';

class IventBoxListView extends StatelessWidget {
  final double? width;
  final double? height;
  final Color color;
  final String iventId;
  final String iventName;
  final String locationName;
  final String? thumbnailUrl;
  final String creatorId;
  final String creatorName;
  final String? creatorImageUrl;
  final bool isFavorited;
  final VoidCallback? onFavorite;

  const IventBoxListView({
    super.key,
    this.width,
    this.height,
    required this.color,
    required this.iventId,
    required this.iventName,
    required this.locationName,
    this.thumbnailUrl,
    required this.creatorId,
    required this.creatorName,
    required this.creatorImageUrl,
    required this.isFavorited,
    this.onFavorite,
  });

  @override
  Widget build(BuildContext context) {
    return IaRoundedContainer(
      onTap: () => Get.toNamed(IventDetailPages.iventDetail, arguments: iventId),
      width: width,
      height: height,
      padding: const EdgeInsets.all(AppDimensions.padding12),
      roundness: AppDimensions.radiusXL,
      color: color,
      child: Column(
        children: [
          IventThumbnail(
            thumbnailUrl: thumbnailUrl,
            gradient: AppColors.gradientBlackS,
            roundness: AppDimensions.radiusM,
            body: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildIventTitle(),
                _buildLocationName(),
              ],
            ),
            buttons: [
              IventFavoriteButton(
                size: AppDimensions.actionButtonSizeS,
                onTap: onFavorite,
                isFavorited: isFavorited,
              ),
              const SizedBox(width: AppDimensions.padding4),
              IventShareButton(
                size: AppDimensions.actionButtonSizeS,
                iventId: iventId,
                iventName: iventName,
                thumbnailUrl: thumbnailUrl,
              ),
            ],
          ),
          const Spacer(),
          _buildCreatorInfo(),
        ],
      ),
    );
  }

  /// Builds the ivent title row with arrow icon
  Widget _buildIventTitle() {
    return Row(
      children: [
        Expanded(
          child: Text(
            iventName,
            style: AppTextStyles.size14BoldWhite,
            maxLines: 1,
            softWrap: false,
          ),
        ),
        const SizedBox(width: AppDimensions.padding12),
        const IaIconButton(iconPath: AppAssets.caretRightSM),
      ],
    );
  }

  /// Builds the location name text
  Widget _buildLocationName() {
    return Text(
      locationName,
      style: AppTextStyles.size10RegularWhite,
      maxLines: 1,
      softWrap: false,
    );
  }

  /// Builds the creator information row
  Widget _buildCreatorInfo() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        CircleAvatar(
          radius: 10,
          backgroundImage: creatorImageUrl != null ? NetworkImage(creatorImageUrl!) : null,
          backgroundColor: AppColors.circleAvatarBackgrond,
        ),
        const SizedBox(width: AppDimensions.padding4),
        Expanded(
          child: Text(
            '@${creatorName}',
            style: AppTextStyles.size12Medium,
            overflow: TextOverflow.fade,
            maxLines: 1,
            softWrap: false,
          ),
        ),
      ],
    );
  }
}
