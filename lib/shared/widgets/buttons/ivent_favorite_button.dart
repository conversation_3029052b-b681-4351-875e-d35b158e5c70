import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_blurred_icon_button.dart';

class IventFavoriteButton extends StatelessWidget {
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;
  final VoidCallback? onTap;
  final double size;
  final bool isFavorited;

  const IventFavoriteButton({
    super.key,
    this.margin,
    this.padding,
    required this.onTap,
    required this.size,
    this.isFavorited = false,
  });

  @override
  Widget build(BuildContext context) {
    return IaBlurredIconButton(
      margin: margin,
      padding: padding,
      color: AppColors.black.withValues(alpha: 0.2),
      onTap: onTap,
      roundness: AppDimensions.radiusS,
      width: AppDimensions.actionButtonSizeS,
      height: AppDimensions.actionButtonSizeS,
      iconPath: isFavorited ? AppAssets.star : AppAssets.star,
      iconColor: isFavorited ? AppColors.starYellow : AppColors.white,
    );
  }
}
