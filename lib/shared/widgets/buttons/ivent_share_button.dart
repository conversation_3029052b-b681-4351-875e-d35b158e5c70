import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/utils/share_utils.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_blurred_icon_button.dart';

class IventShareButton extends StatelessWidget {
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;
  final double size;
  final String iventId;
  final String iventName;
  final String? thumbnailUrl;

  const IventShareButton({
    super.key,
    this.margin,
    this.padding,
    required this.size,
    required this.iventId,
    required this.iventName,
    this.thumbnailUrl,
  });

  void _shareIvent(BuildContext context) {
    ShareUtils.shareIventWithLoading(
      iventId: iventId,
      iventName: iventName,
      thumbnailUrl: thumbnailUrl,
      context: context,
    );
  }

  @override
  Widget build(BuildContext context) {
    return IaBlurredIconButton(
      margin: margin,
      padding: padding,
      color: AppColors.black.withValues(alpha: 0.2),
      onTap: () => _shareIvent(context),
      roundness: AppDimensions.radiusS,
      width: size,
      height: size,
      iconPath: AppAssets.shareAndroid,
      iconColor: AppColors.white,
    );
  }
}
