import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_rounded_button.dart';

class HobbyCatalogueButton extends StatelessWidget {
  final VoidCallback onTap;
  final bool isSelected;
  final String text;
  final EdgeInsetsGeometry? margin;

  const HobbyCatalogueButton({
    super.key,
    required this.onTap,
    required this.isSelected,
    required this.text,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    return IaRoundedButton(
      margin: margin,
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding12),
      height: AppDimensions.buttonHeightFilterSelectedHobbyTag,
      roundness: AppDimensions.buttonRadiusS,
      onTap: onTap,
      color: isSelected ? AppColors.primary : AppColors.lightGrey,
      text: text,
      textStyle: isSelected ? AppTextStyles.size12RegularWhite : AppTextStyles.size12Regular,
      expand: false,
    );
  }
}
