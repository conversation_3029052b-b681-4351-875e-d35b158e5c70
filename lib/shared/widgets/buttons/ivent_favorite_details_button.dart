import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_blurred_icon_button.dart';

class IventFavoriteDetailsButton extends StatelessWidget {
  final VoidCallback onTap;
  final bool isFavorited;

  const IventFavoriteDetailsButton({
    super.key,
    required this.onTap,
    this.isFavorited = false,
  });

  @override
  Widget build(BuildContext context) {
    return IaBlurredIconButton(
      color: AppColors.black.withValues(alpha: 0.2),
      onTap: onTap,
      roundness: AppDimensions.radiusS,
      width: AppDimensions.actionButtonSizeL,
      height: AppDimensions.actionButtonSizeL,
      iconPath: isFavorited ? AppAssets.star : AppAssets.star,
      iconColor: isFavorited ? AppColors.starYellow : AppColors.white,
    );
  }
}
