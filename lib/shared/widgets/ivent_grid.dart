import 'package:flutter/material.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/shared/widgets/ivent_box_list_view.dart';

class IventGrid extends StatefulWidget {
  final List<IventCardItem> iventItems;
  final VoidCallback? onLoadMore;
  final VoidCallback? onRefresh;
  final Function(String iventId) onFavorite;
  final Function(String iventId) getIsFavorite;

  const IventGrid({
    super.key,
    required this.iventItems,
    this.onLoadMore,
    this.onRefresh,
    required this.onFavorite,
    required this.getIsFavorite,
  });

  @override
  State<IventGrid> createState() => _IventGridState();
}

class _IventGridState extends State<IventGrid> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_scrollListener);
  }

  void _scrollListener() {
    if (_scrollController.position.pixels == _scrollController.position.maxScrollExtent) {
      widget.onLoadMore?.call();
    }
  }

  Future<void> _onRefresh() async {
    widget.onRefresh?.call();
  }

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: _onRefresh,
      child: GridView.builder(
        padding: const EdgeInsets.only(bottom: 100), // TODO: Decide on a constant for bottom 100 paddings
        physics: const BouncingScrollPhysics(),
        controller: _scrollController,
        shrinkWrap: true,
        scrollDirection: Axis.vertical,
        itemCount: widget.iventItems.length,
        gridDelegate: const SliverGridDelegateWithMaxCrossAxisExtent(
          maxCrossAxisExtent: 300, // TODO: Move them to constants
          childAspectRatio: 0.87,
          crossAxisSpacing: AppDimensions.padding16,
          mainAxisSpacing: AppDimensions.padding16,
        ),
        itemBuilder: (BuildContext context, int index) {
          final ivent = widget.iventItems[index];
          return IventBoxListView(
            color: AppColors.lightGrey,
            iventId: ivent.iventId,
            iventName: ivent.iventName,
            locationName: ivent.locationName,
            thumbnailUrl: ivent.thumbnailUrl,
            creatorId: ivent.creatorId,
            creatorName: ivent.creatorUsername,
            creatorImageUrl: ivent.creatorImageUrl,
            isFavorited: widget.getIsFavorite(ivent.iventId),
            onFavorite: () => widget.onFavorite(ivent.iventId),
          );
        },
      ),
    );
  }
}
