import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';

class SomethingWentWrong extends StatelessWidget {
  const SomethingWentWrong({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      appBar: AppBar(
        // leading: IaBackButton(),
        leadingWidth: 28,
        backgroundColor: AppColors.white,
        elevation: 0,
      ),
      body: SafeArea(
        child: Center(
          child: Text(
            'Bir şeyler yanlış gitti. Lütfen tekrar deneyin.',
            style: AppTextStyles.size14Bold,
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }
}
