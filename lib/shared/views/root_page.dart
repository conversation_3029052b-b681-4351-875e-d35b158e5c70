import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/widgets/foundation/indicators/ia_loading_indicator.dart';

class RootPage extends StatefulWidget {
  final String routeName;
  final int index;
  final Map<String, String>? parameters;

  const RootPage({
    super.key,
    required this.routeName,
    required this.index,
    this.parameters,
  });

  @override
  State<RootPage> createState() => _RootPageState();
}

class _RootPageState extends State<RootPage> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await Get.offAllNamed(widget.routeName, parameters: widget.parameters, id: widget.index);
    });
  }

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      backgroundColor: AppColors.white,
      body: IaLoadingIndicator(),
    );
  }
}
