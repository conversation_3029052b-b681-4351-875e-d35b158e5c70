//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;


class UsersApi {
  UsersApi([ApiClient? apiClient]) : apiClient = apiClient ?? defaultApiClient;

  final ApiClient apiClient;

  /// Hesap IDsi ile hesap silinir
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<Response> deleteByUserIdWithHttpInfo(String id,) async {
    // ignore: prefer_const_declarations
    final path = r'/users/{id}/delete'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'DELETE',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Hesap IDsi ile hesap silinir
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<void> deleteByUserId(String id,) async {
    final response = await deleteByUserIdWithHttpInfo(id,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Hesap IDsi ile hesap takip edilir
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<Response> followByUserIdWithHttpInfo(String id,) async {
    // ignore: prefer_const_declarations
    final path = r'/users/{id}/follow'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Hesap IDsi ile hesap takip edilir
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<void> followByUserId(String id,) async {
    final response = await followByUserIdWithHttpInfo(id,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Hesap IDsi ile hesap bilgileri listelenir
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<Response> getByUserIdWithHttpInfo(String id,) async {
    // ignore: prefer_const_declarations
    final path = r'/users/{id}'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Hesap IDsi ile hesap bilgileri listelenir
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<GetUserByUserIdReturn?> getByUserId(String id,) async {
    final response = await getByUserIdWithHttpInfo(id,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'GetUserByUserIdReturn',) as GetUserByUserIdReturn;
    
    }
    return null;
  }

  /// Hesap IDsi ile rehber bağlantıları listelenir
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [GetContactsByUserIdDto] getContactsByUserIdDto (required):
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<Response> getContactsByUserIdWithHttpInfo(String id, GetContactsByUserIdDto getContactsByUserIdDto, { int? limit, int? page, }) async {
    // ignore: prefer_const_declarations
    final path = r'/users/{id}/contacts'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody = getContactsByUserIdDto;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (limit != null) {
      queryParams.addAll(_queryParams('', 'limit', limit));
    }
    if (page != null) {
      queryParams.addAll(_queryParams('', 'page', page));
    }

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Hesap IDsi ile rehber bağlantıları listelenir
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [GetContactsByUserIdDto] getContactsByUserIdDto (required):
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<GetContactsByUserIdReturn?> getContactsByUserId(String id, GetContactsByUserIdDto getContactsByUserIdDto, { int? limit, int? page, }) async {
    final response = await getContactsByUserIdWithHttpInfo(id, getContactsByUserIdDto,  limit: limit, page: page, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'GetContactsByUserIdReturn',) as GetContactsByUserIdReturn;
    
    }
    return null;
  }

  /// Hesap IDsi ile hesabın favoriledikleri listelenir
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [String] q:
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<Response> getFavoritesByUserIdWithHttpInfo(String id, { String? q, int? limit, int? page, }) async {
    // ignore: prefer_const_declarations
    final path = r'/users/{id}/favorites'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (q != null) {
      queryParams.addAll(_queryParams('', 'q', q));
    }
    if (limit != null) {
      queryParams.addAll(_queryParams('', 'limit', limit));
    }
    if (page != null) {
      queryParams.addAll(_queryParams('', 'page', page));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Hesap IDsi ile hesabın favoriledikleri listelenir
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [String] q:
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<GetFavoritesByUserIdReturn?> getFavoritesByUserId(String id, { String? q, int? limit, int? page, }) async {
    final response = await getFavoritesByUserIdWithHttpInfo(id,  q: q, limit: limit, page: page, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'GetFavoritesByUserIdReturn',) as GetFavoritesByUserIdReturn;
    
    }
    return null;
  }

  /// Hesap IDsi ile takipçilerden arkadaş olunanları listeler
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [String] q:
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<Response> getFollowerFriendsByUserIdWithHttpInfo(String id, { String? q, int? limit, int? page, }) async {
    // ignore: prefer_const_declarations
    final path = r'/users/{id}/followers/friends'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (q != null) {
      queryParams.addAll(_queryParams('', 'q', q));
    }
    if (limit != null) {
      queryParams.addAll(_queryParams('', 'limit', limit));
    }
    if (page != null) {
      queryParams.addAll(_queryParams('', 'page', page));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Hesap IDsi ile takipçilerden arkadaş olunanları listeler
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [String] q:
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<GetFollowerFriendsByUserIdReturn?> getFollowerFriendsByUserId(String id, { String? q, int? limit, int? page, }) async {
    final response = await getFollowerFriendsByUserIdWithHttpInfo(id,  q: q, limit: limit, page: page, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'GetFollowerFriendsByUserIdReturn',) as GetFollowerFriendsByUserIdReturn;
    
    }
    return null;
  }

  /// Hesap IDsi ile takipçileri listeler
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [String] q:
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<Response> getFollowersByUserIdWithHttpInfo(String id, { String? q, int? limit, int? page, }) async {
    // ignore: prefer_const_declarations
    final path = r'/users/{id}/followers'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (q != null) {
      queryParams.addAll(_queryParams('', 'q', q));
    }
    if (limit != null) {
      queryParams.addAll(_queryParams('', 'limit', limit));
    }
    if (page != null) {
      queryParams.addAll(_queryParams('', 'page', page));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Hesap IDsi ile takipçileri listeler
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [String] q:
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<GetFollowersByUserIdReturn?> getFollowersByUserId(String id, { String? q, int? limit, int? page, }) async {
    final response = await getFollowersByUserIdWithHttpInfo(id,  q: q, limit: limit, page: page, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'GetFollowersByUserIdReturn',) as GetFollowersByUserIdReturn;
    
    }
    return null;
  }

  /// Hesap IDsi ile hesabın takip ettikleri listelenir
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [String] q:
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<Response> getFollowingsByUserIdWithHttpInfo(String id, { String? q, int? limit, int? page, }) async {
    // ignore: prefer_const_declarations
    final path = r'/users/{id}/followings'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (q != null) {
      queryParams.addAll(_queryParams('', 'q', q));
    }
    if (limit != null) {
      queryParams.addAll(_queryParams('', 'limit', limit));
    }
    if (page != null) {
      queryParams.addAll(_queryParams('', 'page', page));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Hesap IDsi ile hesabın takip ettikleri listelenir
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [String] q:
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<GetFollowingsByUserIdReturn?> getFollowingsByUserId(String id, { String? q, int? limit, int? page, }) async {
    final response = await getFollowingsByUserIdWithHttpInfo(id,  q: q, limit: limit, page: page, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'GetFollowingsByUserIdReturn',) as GetFollowingsByUserIdReturn;
    
    }
    return null;
  }

  /// Hesap IDsi ile hesabın katıldığı ya da oluşturduğu iventler listelenir
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [IventListingTypeEnum] type (required):
  ///   Type of ivents to list - either joined or created by the user
  ///
  /// * [String] q:
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<Response> getIventsByUserIdWithHttpInfo(String id, IventListingTypeEnum type, { String? q, int? limit, int? page, }) async {
    // ignore: prefer_const_declarations
    final path = r'/users/{id}/ivents'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

      queryParams.addAll(_queryParams('', 'type', type));
    if (q != null) {
      queryParams.addAll(_queryParams('', 'q', q));
    }
    if (limit != null) {
      queryParams.addAll(_queryParams('', 'limit', limit));
    }
    if (page != null) {
      queryParams.addAll(_queryParams('', 'page', page));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Hesap IDsi ile hesabın katıldığı ya da oluşturduğu iventler listelenir
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [IventListingTypeEnum] type (required):
  ///   Type of ivents to list - either joined or created by the user
  ///
  /// * [String] q:
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<GetIventsByUserIdReturn?> getIventsByUserId(String id, IventListingTypeEnum type, { String? q, int? limit, int? page, }) async {
    final response = await getIventsByUserIdWithHttpInfo(id, type,  q: q, limit: limit, page: page, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'GetIventsByUserIdReturn',) as GetIventsByUserIdReturn;
    
    }
    return null;
  }

  /// Hesap IDsi ile hesabın aşaması gösterilir
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<Response> getLevelByUserIdWithHttpInfo(String id,) async {
    // ignore: prefer_const_declarations
    final path = r'/users/{id}/level'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Hesap IDsi ile hesabın aşaması gösterilir
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<GetLevelByUserIdReturn?> getLevelByUserId(String id,) async {
    final response = await getLevelByUserIdWithHttpInfo(id,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'GetLevelByUserIdReturn',) as GetLevelByUserIdReturn;
    
    }
    return null;
  }

  /// Hesap IDsi ile hesabın memoriesleri listelenir
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<Response> getMemoryFoldersByUserIdWithHttpInfo(String id, { int? limit, int? page, }) async {
    // ignore: prefer_const_declarations
    final path = r'/users/{id}/memories'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (limit != null) {
      queryParams.addAll(_queryParams('', 'limit', limit));
    }
    if (page != null) {
      queryParams.addAll(_queryParams('', 'page', page));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Hesap IDsi ile hesabın memoriesleri listelenir
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<GetMemoryFoldersByUserIdReturn?> getMemoryFoldersByUserId(String id, { int? limit, int? page, }) async {
    final response = await getMemoryFoldersByUserIdWithHttpInfo(id,  limit: limit, page: page, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'GetMemoryFoldersByUserIdReturn',) as GetMemoryFoldersByUserIdReturn;
    
    }
    return null;
  }

  /// Hesap IDsi ile hesaba ait sayfalar gösterilir
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<Response> getPagesByUserIdWithHttpInfo(String id, { int? limit, int? page, }) async {
    // ignore: prefer_const_declarations
    final path = r'/users/{id}/pages'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (limit != null) {
      queryParams.addAll(_queryParams('', 'limit', limit));
    }
    if (page != null) {
      queryParams.addAll(_queryParams('', 'page', page));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Hesap IDsi ile hesaba ait sayfalar gösterilir
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<GetPagesByUserIdReturn?> getPagesByUserId(String id, { int? limit, int? page, }) async {
    final response = await getPagesByUserIdWithHttpInfo(id,  limit: limit, page: page, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'GetPagesByUserIdReturn',) as GetPagesByUserIdReturn;
    
    }
    return null;
  }

  /// Hesap IDsi ile hesabın avatar URL'si ve ismi listelenir
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<Response> getUserBannerByUserIdWithHttpInfo(String id,) async {
    // ignore: prefer_const_declarations
    final path = r'/users/{id}/banner'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Hesap IDsi ile hesabın avatar URL'si ve ismi listelenir
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<GetUserBannerByUserIdReturn?> getUserBannerByUserId(String id,) async {
    final response = await getUserBannerByUserIdWithHttpInfo(id,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'GetUserBannerByUserIdReturn',) as GetUserBannerByUserIdReturn;
    
    }
    return null;
  }

  /// Hesap IDsi ile hesabın vibeları listelenir
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<Response> getVibeFoldersByUserIdWithHttpInfo(String id, { int? limit, int? page, }) async {
    // ignore: prefer_const_declarations
    final path = r'/users/{id}/vibes'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    if (limit != null) {
      queryParams.addAll(_queryParams('', 'limit', limit));
    }
    if (page != null) {
      queryParams.addAll(_queryParams('', 'page', page));
    }

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'GET',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Hesap IDsi ile hesabın vibeları listelenir
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [int] limit:
  ///
  /// * [int] page:
  Future<GetVibeFoldersByUserIdReturn?> getVibeFoldersByUserId(String id, { int? limit, int? page, }) async {
    final response = await getVibeFoldersByUserIdWithHttpInfo(id,  limit: limit, page: page, );
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'GetVibeFoldersByUserIdReturn',) as GetVibeFoldersByUserIdReturn;
    
    }
    return null;
  }

  /// Hesaba giriş yapılır
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [RegisterDto] registerDto (required):
  Future<Response> registerWithHttpInfo(RegisterDto registerDto,) async {
    // ignore: prefer_const_declarations
    final path = r'/users/register';

    // ignore: prefer_final_locals
    Object? postBody = registerDto;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Hesaba giriş yapılır
  ///
  /// Parameters:
  ///
  /// * [RegisterDto] registerDto (required):
  Future<RegisterReturn?> register(RegisterDto registerDto,) async {
    final response = await registerWithHttpInfo(registerDto,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
    // When a remote server returns no body with a status of 204, we shall not decode it.
    // At the time of writing this, `dart:convert` will throw an "Unexpected end of input"
    // FormatException when trying to decode an empty string.
    if (response.body.isNotEmpty && response.statusCode != HttpStatus.noContent) {
      return await apiClient.deserializeAsync(await _decodeBodyBytes(response), 'RegisterReturn',) as RegisterReturn;
    
    }
    return null;
  }

  /// Hesap IDsi ile hesabın takipçilerinden kaldırılır
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [RemoveFollowerByUserIdDto] removeFollowerByUserIdDto (required):
  Future<Response> removeFollowerByUserIdWithHttpInfo(String id, RemoveFollowerByUserIdDto removeFollowerByUserIdDto,) async {
    // ignore: prefer_const_declarations
    final path = r'/users/{id}/followers/remove'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody = removeFollowerByUserIdDto;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Hesap IDsi ile hesabın takipçilerinden kaldırılır
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [RemoveFollowerByUserIdDto] removeFollowerByUserIdDto (required):
  Future<void> removeFollowerByUserId(String id, RemoveFollowerByUserIdDto removeFollowerByUserIdDto,) async {
    final response = await removeFollowerByUserIdWithHttpInfo(id, removeFollowerByUserIdDto,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Creator başvuru formu gönderilir
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<Response> sendCreatorRequestFormWithHttpInfo(String id,) async {
    // ignore: prefer_const_declarations
    final path = r'/users/{id}/sendCreatorRequest'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Creator başvuru formu gönderilir
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<void> sendCreatorRequestForm(String id,) async {
    final response = await sendCreatorRequestFormWithHttpInfo(id,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Email doğrulaması için kod gönderilir
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<Response> sendVerificationEmailWithHttpInfo(String id,) async {
    // ignore: prefer_const_declarations
    final path = r'/users/{id}/sendVerificationEmail'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Email doğrulaması için kod gönderilir
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<void> sendVerificationEmail(String id,) async {
    final response = await sendVerificationEmailWithHttpInfo(id,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Hesap IDsi ile hesap bildirimleri açılır
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<Response> subscribeByUserIdWithHttpInfo(String id,) async {
    // ignore: prefer_const_declarations
    final path = r'/users/{id}/subscribe'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Hesap IDsi ile hesap bildirimleri açılır
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<void> subscribeByUserId(String id,) async {
    final response = await subscribeByUserIdWithHttpInfo(id,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Hesap IDsi ile hesabın takibinden çıkılır
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<Response> unfollowByUserIdWithHttpInfo(String id,) async {
    // ignore: prefer_const_declarations
    final path = r'/users/{id}/unfollow'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Hesap IDsi ile hesabın takibinden çıkılır
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<void> unfollowByUserId(String id,) async {
    final response = await unfollowByUserIdWithHttpInfo(id,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Hesap IDsi ile hesap bildirimleri kapatılır
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<Response> unsubscribeByUserIdWithHttpInfo(String id,) async {
    // ignore: prefer_const_declarations
    final path = r'/users/{id}/unsubscribe'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Hesap IDsi ile hesap bildirimleri kapatılır
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<void> unsubscribeByUserId(String id,) async {
    final response = await unsubscribeByUserIdWithHttpInfo(id,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Hesap IDsi ile hesabın detayları güncellenir
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [UpdateByUserIdDto] updateByUserIdDto (required):
  Future<Response> updateByUserIdWithHttpInfo(String id, UpdateByUserIdDto updateByUserIdDto,) async {
    // ignore: prefer_const_declarations
    final path = r'/users/{id}/update'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody = updateByUserIdDto;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'PUT',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Hesap IDsi ile hesabın detayları güncellenir
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [UpdateByUserIdDto] updateByUserIdDto (required):
  Future<void> updateByUserId(String id, UpdateByUserIdDto updateByUserIdDto,) async {
    final response = await updateByUserIdWithHttpInfo(id, updateByUserIdDto,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Hesap IDsi ile emaili güncellenir
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [UpdateEmailByUserIdDto] updateEmailByUserIdDto (required):
  Future<Response> updateEmailByUserIdWithHttpInfo(String id, UpdateEmailByUserIdDto updateEmailByUserIdDto,) async {
    // ignore: prefer_const_declarations
    final path = r'/users/{id}/update/email'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody = updateEmailByUserIdDto;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'PUT',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Hesap IDsi ile emaili güncellenir
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [UpdateEmailByUserIdDto] updateEmailByUserIdDto (required):
  Future<void> updateEmailByUserId(String id, UpdateEmailByUserIdDto updateEmailByUserIdDto,) async {
    final response = await updateEmailByUserIdWithHttpInfo(id, updateEmailByUserIdDto,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Hesap IDsi ile mezuniyet durumu güncellenir
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [UpdateGradByUserIdDto] updateGradByUserIdDto (required):
  Future<Response> updateGradByUserIdWithHttpInfo(String id, UpdateGradByUserIdDto updateGradByUserIdDto,) async {
    // ignore: prefer_const_declarations
    final path = r'/users/{id}/update/grad'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody = updateGradByUserIdDto;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'PUT',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Hesap IDsi ile mezuniyet durumu güncellenir
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [UpdateGradByUserIdDto] updateGradByUserIdDto (required):
  Future<void> updateGradByUserId(String id, UpdateGradByUserIdDto updateGradByUserIdDto,) async {
    final response = await updateGradByUserIdWithHttpInfo(id, updateGradByUserIdDto,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Hesap IDsi ile bildirim ayarları güncellenir
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [Object] body (required):
  Future<Response> updateNotificationsByUserIdWithHttpInfo(String id, Object body,) async {
    // ignore: prefer_const_declarations
    final path = r'/users/{id}/update/notifications'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody = body;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'PUT',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Hesap IDsi ile bildirim ayarları güncellenir
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [Object] body (required):
  Future<void> updateNotificationsByUserId(String id, Object body,) async {
    final response = await updateNotificationsByUserIdWithHttpInfo(id, body,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Hesap IDsi ile telefon numarası güncellenir
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [UpdatePhoneNumberByUserIdDto] updatePhoneNumberByUserIdDto (required):
  Future<Response> updatePhoneNumberByUserIdWithHttpInfo(String id, UpdatePhoneNumberByUserIdDto updatePhoneNumberByUserIdDto,) async {
    // ignore: prefer_const_declarations
    final path = r'/users/{id}/update/phoneNumber'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody = updatePhoneNumberByUserIdDto;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>['application/json'];


    return apiClient.invokeAPI(
      path,
      'PUT',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Hesap IDsi ile telefon numarası güncellenir
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  ///
  /// * [UpdatePhoneNumberByUserIdDto] updatePhoneNumberByUserIdDto (required):
  Future<void> updatePhoneNumberByUserId(String id, UpdatePhoneNumberByUserIdDto updatePhoneNumberByUserIdDto,) async {
    final response = await updatePhoneNumberByUserIdWithHttpInfo(id, updatePhoneNumberByUserIdDto,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Hesap IDsi ile gizlilik ayarları güncellenir
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<Response> updatePrivacyByUserIdWithHttpInfo(String id,) async {
    // ignore: prefer_const_declarations
    final path = r'/users/{id}/update/privacy'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'PUT',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Hesap IDsi ile gizlilik ayarları güncellenir
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<void> updatePrivacyByUserId(String id,) async {
    final response = await updatePrivacyByUserIdWithHttpInfo(id,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }

  /// Email doğrulaması için gönderilen kod doğrulanır
  ///
  /// Note: This method returns the HTTP [Response].
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<Response> validateEmailWithHttpInfo(String id,) async {
    // ignore: prefer_const_declarations
    final path = r'/users/{id}/emailVerification'
      .replaceAll('{id}', id);

    // ignore: prefer_final_locals
    Object? postBody;

    final queryParams = <QueryParam>[];
    final headerParams = <String, String>{};
    final formParams = <String, String>{};

    const contentTypes = <String>[];


    return apiClient.invokeAPI(
      path,
      'POST',
      queryParams,
      postBody,
      headerParams,
      formParams,
      contentTypes.isEmpty ? null : contentTypes.first,
    );
  }

  /// Email doğrulaması için gönderilen kod doğrulanır
  ///
  /// Parameters:
  ///
  /// * [String] id (required):
  Future<void> validateEmail(String id,) async {
    final response = await validateEmailWithHttpInfo(id,);
    if (response.statusCode >= HttpStatus.badRequest) {
      throw ApiException(response.statusCode, await _decodeBodyBytes(response));
    }
  }
}

