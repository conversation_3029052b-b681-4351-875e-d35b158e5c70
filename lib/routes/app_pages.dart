import 'package:get/get.dart';
import 'package:ivent_app/core/middlewares/auth_middleware.dart';
import 'package:ivent_app/features/app_navigation/views/app_navigation.dart';
import 'package:ivent_app/features/auth/auth_pages.dart';
import 'package:ivent_app/features/home/<USER>';
import 'package:ivent_app/features/ivent_create/ivent_create_pages.dart';
import 'package:ivent_app/features/ivent_detail/ivent_detail_pages.dart';
import 'package:ivent_app/features/profile/profile_pages.dart';
import 'package:ivent_app/features/splash/splash_screen.dart';
import 'package:ivent_app/routes/creator_request.dart';
import 'package:ivent_app/routes/notifications.dart';
import 'package:ivent_app/routes/other.dart';
import 'package:ivent_app/routes/page_creation.dart';
import 'package:ivent_app/routes/page_detail.dart';
import 'package:ivent_app/routes/settings.dart';
import 'package:ivent_app/routes/side_menu.dart';
import 'package:ivent_app/routes/vibes.dart';
import 'package:ivent_app/shared/views/something_went_wrong.dart';

class AppPages {
  AppPages._();

  static const initial = splash;
  static const splash = '/splash';
  static const appNavigation = '/appNavigation';
  static const notFound = '/notFound';

  static final notFoundPage = GetPage(
    name: notFound,
    page: () => const SomethingWentWrong(),
  );

  static final routes = [
    GetPage(
      name: splash,
      page: () => const SplashScreen(),
    ),
    GetPage(
      name: appNavigation,
      page: () => const AppNavigation(),
      middlewares: [AuthMiddleware()],
    ),
    ...AuthPages.routes,
    ...CreatorRequestPages.routes,
    ...HomePages.routes,
    ...IventCreatePages.routes,
    ...IventDetailPages.routes,
    ...NotificationsPages.routes,
    ...OtherPages.routes,
    ...PageCreationPages.routes,
    ...PageDetailPages.routes,
    ...ProfilePages.routes,
    ...SettingsPages.routes,
    ...SideMenuPages.routes,
    ...VibesPages.routes,
  ];
}
