import 'package:get/get.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/notifications/controllers/notification_controller.dart';
import 'package:ivent_app/features/notifications/controllers/notification_state_manager.dart';
import 'package:ivent_app/features/notifications/views/notifications_page.dart';

class NotificationsPages {
  NotificationsPages._();

  static const _prefix = '/notifications';

  static const notifications = '$_prefix';

  static final routes = [
    GetPage(
      name: notifications,
      page: () => const NotificationsPage(),
      binding: NotificationsBindings(),
    ),
  ];
}

class NotificationsBindings implements Bindings {
  @override
  void dependencies() {
    if (Get.isRegistered<NotificationController>()) return;
    Get.lazyPut<NotificationController>(
        () => NotificationController(Get.find<AuthService>(), NotificationSharedState()),
        fenix: true);
  }
}
