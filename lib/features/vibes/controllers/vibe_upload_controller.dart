import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/core/widgets/ia_search_results_builder.dart';
import 'package:ivent_app/features/vibes/controllers/vibes_state_manager.dart';

/// Controller for handling vibe upload functionality
///
/// Manages captured media (photos/videos) from camera and handles
/// the upload process to the server with proper media type detection.
class VibeUploadController extends BaseControllerWithSearch<VibesSharedState> {
  final _iventsResult = Rxn<GetIventsByUserIdReturn>();

  GetIventsByUserIdReturn? get iventsResult => _iventsResult.value;

  VibeUploadController(AuthService authService, VibesSharedState state) : super(authService, state);

  @override
  bool get isResultsEmpty => iventsResult?.ivents.isEmpty ?? true;

  @override
  InitialSearchBehavior get initialSearchBehavior => InitialSearchBehavior.loaded;

  @override
  Future<void> onSearch([String? query]) async {
    _iventsResult.value = await usersApi.getIventsByUserId(
      sessionUser.sessionId,
      IventListingTypeEnum.joined,
      q: query,
      limit: 5,
    );
    print(iventsResult);
  }

  // Observable properties
  final _capturedMediaPath = ''.obs;
  final _isVideo = false.obs;
  final _isFrontCamera = false.obs;

  final _selectedVibeFolderId = Rxn<String>();

  // Getters
  String get capturedMediaPath => _capturedMediaPath.value;
  bool get isVideo => _isVideo.value;
  bool get isFrontCamera => _isFrontCamera.value;

  String? get selectedVibeFolderId => _selectedVibeFolderId.value;

  // Setters
  set capturedMediaPath(String value) => _capturedMediaPath.value = value;
  set isVideo(bool value) => _isVideo.value = value;
  set isFrontCamera(bool value) => _isFrontCamera.value = value;

  void selectIvent(String iventId) => _selectedVibeFolderId.value = iventId;

  Future<void> uploadVibe() async {
    await runSafe(tag: 'uploadVibe', () async {
      if (selectedVibeFolderId == null) return;
      await Future.delayed(const Duration(seconds: 1));
      print('Uploading vibe to folder: $selectedVibeFolderId');

      // TODO: HANDLE THIS!
      // ------------------------------------------------------------------------

      final uri = Uri.parse('${authService.basePath}/vibes/create');

      final request = http.MultipartRequest('POST', uri)
        ..fields['vibeFolderId'] = selectedVibeFolderId!
        ..fields['privacy'] = 'public'
        ..files.add(await http.MultipartFile.fromPath(
          'file', // <-- must match backend field name
          capturedMediaPath,
          contentType: isVideo ? MediaType('video', 'mp4') : MediaType('image', 'jpeg'),
        ));
      // Add authorization header
      request.headers['Authorization'] = 'Bearer ${authService.sessionUser!.token}';

      await request.send();
      Get.until((route) => route.isFirst);
      return;

      // ------------------------------------------------------------------------

      // await vibesApi.createVibe(
      //   await http.MultipartFile.fromPath(
      //     'file',
      //     capturedMediaPath,
      //     contentType: isVideo ? MediaType('video', 'mp4') : MediaType('image', 'jpeg'),
      //   ),
      //   selectedVibeFolderId!,
      //   VibePrivacyEnum.public,
      // );
      // // get remove the last 2 pages from the stack
      // Get.offAllNamed(AppPages.appNavigation);
    });
  }
}
