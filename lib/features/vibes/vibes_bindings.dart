import 'package:get/get.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/vibes/controllers/vibe_upload_controller.dart';
import 'package:ivent_app/features/vibes/controllers/vibes_page_controller.dart';
import 'package:ivent_app/features/vibes/controllers/vibes_state_manager.dart';

class VibesBindings implements Bindings {
  @override
  void dependencies() {
    final service = Get.find<AuthService>();
    final vibesSharedState = Get.put(VibesSharedState(), permanent: true);
    
    Get.lazyPut(() => VibesPageController(service, vibesSharedState), fenix: true);
    Get.lazyPut(() => VibeUploadController(service, vibesSharedState), fenix: true);
  }
}
