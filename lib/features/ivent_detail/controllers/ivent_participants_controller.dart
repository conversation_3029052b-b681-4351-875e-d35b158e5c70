import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/ivent_detail/controllers/ivent_detail_state_manager.dart';
import 'package:ivent_app/features/ivent_detail/ivent_detail_pages.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';

class IventParticipantsController extends BaseController<IventDetailSharedState> {
  final _participants = Rxn<SearchParticipantsByIventIdReturn>();

  IventParticipantsController(
    AuthService authService,
    IventDetailSharedState state,
  ) : super(authService, state);

  SearchParticipantsByIventIdReturn? get participants => _participants.value;

  set participants(SearchParticipantsByIventIdReturn? value) => _participants.value = value;

  Future<void> getParticipantsPage(
    IventViewTypeEnum viewType,
    int participantCount, {
    String? q,
  }) async {
    if (viewType == IventViewTypeEnum.default_ && participantCount == 0) {
      return;
    }

    await runSafe(
      () async {
        participants = await squadMembershipsApi.searchParticipantsByIventId(
          state.iventId,
          q: q,
        );

        _navigateToParticipantsPage(viewType);
      },
      tag: 'getParticipantsPage',
    );
  }

  void _navigateToParticipantsPage(IventViewTypeEnum viewType) {
    switch (viewType) {
      case IventViewTypeEnum.joined:
        Get.toNamed(IventDetailPages.squad, arguments: state.iventId);
        break;
      case IventViewTypeEnum.created:
      default:
        Get.toNamed(IventDetailPages.participants, arguments: state.iventId);
        break;
    }
  }
}
