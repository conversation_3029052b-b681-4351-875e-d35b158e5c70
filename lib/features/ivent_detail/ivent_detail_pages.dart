import 'package:get/get.dart';
import 'package:ivent_app/features/ivent_detail/ivent_detail_bindings.dart';
import 'package:ivent_app/features/ivent_detail/views/ivent_detail.dart';
import 'package:ivent_app/features/ivent_detail/views/ivent_detail_collabrators.dart';
import 'package:ivent_app/features/ivent_detail/views/ivent_detail_invite_more_people.dart';
import 'package:ivent_app/features/ivent_detail/views/ivent_detail_participants.dart';
import 'package:ivent_app/features/ivent_detail/views/ivent_detail_squad.dart';
import 'package:ivent_app/features/ivent_detail/views/ivent_detail_whom_you_join.dart';

class IventDetailPages {
  IventDetailPages._();

  static const _prefix = '/iventDetail';

  static const iventDetail = '$_prefix';
  static const whomYouJoin = '$_prefix/whomYouJoin';
  static const inviteMorePeople = '$_prefix/inviteMorePeople';
  static const participants = '$_prefix/participants';
  static const collaborators = '$_prefix/collaborators';
  static const squad = '$_prefix/squad';

  static final routes = [
    GetPage(
      name: iventDetail,
      page: () => IventDetail(Get.arguments),
      binding: IventDetailBindings(),
    ),
    GetPage(name: whomYouJoin, page: () => IventDetailWhomYouJoin(iventId: Get.arguments)),
    GetPage(name: inviteMorePeople, page: () => IventDetailInviteMorePeople(iventId: Get.arguments)),
    GetPage(name: participants, page: () => IventDetailParticipants(Get.arguments)),
    GetPage(name: collaborators, page: () => IventDetailCollabrators(iventId: Get.arguments)),
    GetPage(name: squad, page: () => IventDetailSquad(Get.arguments)),
  ];
}
