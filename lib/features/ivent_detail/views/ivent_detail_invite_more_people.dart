import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_floating_action_button.dart';
import 'package:ivent_app/core/widgets/ia_search_results_builder.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/ivent_detail/controllers/ivent_participation_controller.dart';
import 'package:ivent_app/features/ivent_detail/widgets/lists/ia_selection_list.dart';


class IventDetailInviteMorePeople extends GetView<IventParticipationController> {
  final String iventId;

  const IventDetailInviteMorePeople({super.key, required this.iventId});

  @override
  String? get tag => iventId;

  @override
  Widget build(BuildContext context) {
    return IaScaffold.search(
      title: '<PERSON><PERSON>',
      textEditingController: controller.textController,
      body: Obx(() {
        final groupsResult = controller.groupsResult;
        final usersResult = controller.usersResult;
        return IaSearchResultsBuilder(
          entityName: 'Arkadaş',
          isSearching: controller.isSearching,
          isResultsEmpty: controller.isResultsEmpty,
          isQueryEmpty: controller.isQueryEmpty,
          initialSearchBehavior: InitialSearchBehavior.loaded,
          builder: (context) => SingleChildScrollView(
            child: Column(
              children: [
                if ((groupsResult?.groupCount ?? 0) != 0) groupSelectionList(groupsResult!, controller),
                if ((usersResult?.friendCount ?? 0) != 0) userSelectionList(usersResult!, controller),
                const SizedBox(height: 100),
              ],
            ),
          ),
        );
      }),
      floatingActionButton: Obx(() => IaFloatingActionButton(
        onPressed: controller.inviteFriends,
        isEnabled: controller.selectedFriendCount != 0,
        isLoading: controller.isLoading('inviteFriends'),
        text: controller.selectedFriendCount != 0
            ? '${controller.selectedFriendCount} Kişiyi Çağır'
            : 'Birden Fazla Seçebilirsin',
      )),
    );
  }
}
