import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/widgets/composite/tiles/ia_list_tile.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_floating_action_button.dart';
import 'package:ivent_app/core/widgets/foundation/graphics/ia_svg_icon.dart';
import 'package:ivent_app/core/widgets/ia_search_results_builder.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/ivent_detail/controllers/ivent_participation_controller.dart';
import 'package:ivent_app/features/ivent_detail/widgets/lists/ia_selection_list.dart';

class IventDetailWhomYou<PERSON>oin extends GetView<IventParticipationController> {
  final String iventId;

  const IventDetailWhomYouJoin({super.key, required this.iventId});

  @override
  String? get tag => iventId;

  @override
  Widget build(BuildContext context) {
    return IaScaffold.search(
      title: 'Takipçiler',
      textEditingController: controller.textController,
      body: Obx(() {
        final groupsResult = controller.groupsResult;
        final usersResult = controller.usersResult;
        return IaSearchResultsBuilder(
          entityName: 'Arkadaş',
          isSearching: controller.isSearching,
          isResultsEmpty: false,
          isQueryEmpty: controller.isQueryEmpty,
          initialSearchBehavior: InitialSearchBehavior.loaded,
          builder: (context) => SingleChildScrollView(
            child: Column(
              children: [
                _buildSingleParticipationTile(controller),
                const SizedBox(height: AppDimensions.padding20),
                if ((groupsResult?.groupCount ?? 0) != 0) groupSelectionList(groupsResult!, controller),
                if ((usersResult?.friendCount ?? 0) != 0) userSelectionList(usersResult!, controller),
                const SizedBox(height: 100),
              ],
            ),
          ),
        );
      }),
      floatingActionButton: Obx(() => IaFloatingActionButton(
            onPressed: controller.inviteFriends,
            isEnabled: controller.selectedFriendCount != 0,
            isLoading: controller.isLoading('joinIvent'),
            text: controller.selectedFriendCount != 0
                ? '${controller.selectedFriendCount} ' + 'Arkadaşınla Katıl'
                : 'Birden Fazla Seçebilirsin',
          )),
    );
  }

  Widget _buildSingleParticipationTile(IventParticipationController controller) {
    return IaListTile.withImageUrl(
      avatarUrl: controller.sessionUser.sessionAvatarUrl,
      title: '${controller.sessionUser.sessionFullname} (Sen)',
      subtitle: 'Tek Katılıyorum',
      onTap: () {
        controller.toggleInvitableUser(UserListItem(
          userId: controller.sessionUser.sessionId,
          username: controller.sessionUser.sessionUsername,
          avatarUrl: controller.sessionUser.sessionAvatarUrl,
          university: '',
        ));
        controller.joinIvent();
      },
      trailing: const IaSvgIcon(iconPath: AppAssets.chevronRight, iconColor: AppColors.darkGrey),
    );
  }
}
