import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/composite/tiles/ia_list_tile.dart';
import 'package:ivent_app/core/widgets/foundation/indicators/ia_loading_indicator.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/ivent_detail/controllers/ivent_details_controller.dart';
import 'package:ivent_app/features/profile/profile_pages.dart';

/// iVent detail participants page displaying list of participants
///
/// Shows all participants of an iVent with search functionality.
/// Follows the architecture guide's page structure patterns with proper
/// lifecycle management and resource cleanup.
class IventDetailParticipants extends StatefulWidget {
  /// The unique identifier for the iVent
  final String iventId;

  const IventDetailParticipants(this.iventId, {Key? key}) : super(key: key);

  @override
  State<IventDetailParticipants> createState() => _IventDetailParticipantsState();
}

class _IventDetailParticipantsState extends State<IventDetailParticipants> {
  // Controllers
  late final IventDetailsController _controller;
  late final TextEditingController _searchBarController;

  @override
  void initState() {
    super.initState();
    _controller = Get.find(tag: widget.iventId);
    _searchBarController = TextEditingController();
  }

  @override
  void dispose() {
    _searchBarController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return IaScaffold.search(
      title: _controller.iventInfoController.iventPage!.iventName,
      textEditingController: _searchBarController,
      body: Obx(() {
        final pageContent = _controller.participantsController.participants;
        if (pageContent == null) {
          return const IaLoadingIndicator();
        }
        return ListView.separated(
          padding: const EdgeInsets.only(bottom: 100),
          itemCount: pageContent.userCount,
          itemBuilder: (context, index) {
            final element = pageContent.users[index];
            return IaListTile.withImageUrl(
              onTap: () => Get.toNamed(ProfilePages.userProfile, arguments: element.userId),
              avatarUrl: element.avatarUrl,
              title: '@${element.username}',
              subtitle: element.university,
              trailing: Text('Etkinliğe Katılıyor', style: AppTextStyles.size12RegularTextSecondary),
            );
          },
          separatorBuilder: IaListTile.separatorBuilder20,
        );
      }),
    );
  }
}
