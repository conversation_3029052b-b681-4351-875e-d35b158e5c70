import 'package:get/get.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/auth/controllers/auth_contacts_controller.dart';
import 'package:ivent_app/features/auth/controllers/auth_phone_controller.dart';
import 'package:ivent_app/features/auth/controllers/auth_registration_controller.dart';
import 'package:ivent_app/features/auth/controllers/auth_shared_state.dart';
import 'package:ivent_app/features/auth/controllers/auth_validation_controller.dart';

class AuthBindings implements Bindings {
  @override
  void dependencies() {
    final service = Get.find<AuthService>();
    final sharedState = Get.put(AuthSharedState());

    Get.lazyPut<AuthPhoneController>(() => AuthPhoneController(service, sharedState), fenix: true);
    Get.lazyPut<AuthContactsController>(() => AuthContactsController(service, sharedState), fenix: true);
    Get.lazyPut<AuthRegistrationController>(() => AuthRegistrationController(service, sharedState), fenix: true);
    Get.lazyPut<AuthValidationController>(() => AuthValidationController(service, sharedState), fenix: true);
  }
}
