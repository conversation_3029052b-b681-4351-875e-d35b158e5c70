import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_floating_action_button.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/auth/constants/strings.dart';
import 'package:ivent_app/features/auth/widgets/onboarding_content_widget.dart';
import 'package:ivent_app/features/auth/auth_pages.dart';

class OnboardingPage extends StatefulWidget {
  const OnboardingPage({super.key});

  @override
  State<OnboardingPage> createState() => _OnboardingPageState();
}

class _OnboardingPageState extends State<OnboardingPage> {
  int _currentPage = 0;

  static const List<String> _imagePaths = [
    AppAssets.onboarding1,
    AppAssets.onboarding2,
    AppAssets.onboarding3,
  ];

  @override
  Widget build(BuildContext context) {
    return IaScaffold.onboarding(
      body: Column(
        children: [
          Expanded(child: _buildOnboardingContent()),
          _buildActionButton(),
        ],
      ),
    );
  }

  Widget _buildOnboardingContent() {
    return OnboardingContentWidget(
      currentPage: _currentPage,
      imagePaths: _imagePaths,
      onPageIndicatorTapped: _handlePageIndicatorTapped,
    );
  }

  Widget _buildActionButton() {
    return IaFloatingActionButton(
      isEnabled: true,
      text: _getActionButtonText(),
      onPressed: _handleActionButtonPressed,
    );
  }

  void _handlePageIndicatorTapped(int pageIndex) {
    if (pageIndex >= 0 && pageIndex < _imagePaths.length) {
      setState(() {
        _currentPage = pageIndex;
      });
    }
  }

  void _handleActionButtonPressed() {
    setState(() {
      if (_currentPage < _imagePaths.length - 1) {
        _currentPage++;
      } else {
        Get.toNamed(AuthPages.phonePage);
      }
    });
  }

  String _getActionButtonText() {
    return _currentPage < _imagePaths.length - 1 ? AuthStrings.devamEt : AuthStrings.hemenBaslat;
  }
}
