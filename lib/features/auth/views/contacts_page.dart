import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/composite/buttons/shared_buttons.dart';
import 'package:ivent_app/core/widgets/composite/tiles/ia_list_tile.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_floating_action_button.dart';
import 'package:ivent_app/core/widgets/foundation/indicators/ia_loading_indicator.dart';
import 'package:ivent_app/core/widgets/ia_search_results_builder.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/auth/constants/strings.dart';
import 'package:ivent_app/features/auth/controllers/auth_contacts_controller.dart';
import 'package:ivent_app/routes/app_pages.dart';

class ContactsPage extends GetView<AuthContactsController> {
  const ContactsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return IaScaffold.search(
      title: AuthStrings.kisileriniz,
      textEditingController: controller.textController,
      body: controller.state.accessToContacts ? _buildContactsList() : _buildAccessDeniedMessage(),
      floatingActionButton: IaFloatingActionButton(
        isEnabled: true,
        text: AuthStrings.uyeligiTamamla,
        onPressed: _handleCompletePressed,
      ),
    );
  }

  Widget _buildContactsList() {
    return Obx(() {
      final contactsData = controller.getContactsReturn;
      if (contactsData == null) return const IaLoadingIndicator();
      return IaSearchResultsBuilder(
        entityName: 'Kullanıcı',
        isSearching: controller.isSearching,
        isResultsEmpty: controller.isResultsEmpty,
        isQueryEmpty: controller.isQueryEmpty,
        initialSearchBehavior: InitialSearchBehavior.loaded,
        builder: (context) {
          return ListView.separated(
            padding: const EdgeInsets.only(bottom: 100),
            itemCount: contactsData.contactCount,
            itemBuilder: (context, index) => _buildContactItem(contactsData.contacts[index]),
            separatorBuilder: IaListTile.separatorBuilder20,
          );
        },
      );
    });
  }

  Widget _buildContactItem(UserListItemWithPhoneNumber contact) {
    return IaListTile.withImageUrl(
      avatarUrl: contact.avatarUrl,
      title: '@${contact.username}',
      subtitle: contact.university,
      trailing: SharedButtons.addFriendListTile(
        onTap: () => _handleFriendRequestToggle(contact.userId),
        relationshipStatus: controller.getRelationshipStatus(contact.userId)!,
      ),
    );
  }

  Widget _buildAccessDeniedMessage() {
    return Column(
      children: [
        Expanded(
          child: Center(
            child: Text(
              'Kişilerinize erişim izni vermediniz.',
              style: AppTextStyles.size24Bold,
            ),
          ),
        ),
        const SizedBox(height: 100),
      ],
    );
  }

  void _handleCompletePressed() => Get.toNamed(AppPages.appNavigation);

  void _handleFriendRequestToggle(String userId) => controller.toggleFriendRequest(userId);
}
