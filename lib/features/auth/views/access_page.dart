import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_floating_action_button.dart';
import 'package:ivent_app/core/widgets/foundation/containers/ia_rounded_container.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/auth/auth_pages.dart';
import 'package:ivent_app/features/auth/controllers/auth_shared_state.dart';
import 'package:permission_handler/permission_handler.dart';

class AccessPage extends StatefulWidget {
  const AccessPage({super.key});

  @override
  State<AccessPage> createState() => _AccessPageState();
}

class _AccessPageState extends State<AccessPage> {
  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _requestContactsPermission();
    });
  }

  @override
  Widget build(BuildContext context) {
    return IaScaffold.noSearch(
      title: 'Erişim İsteği',
      bodyPadding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
      body: Column(
        children: [
          const SizedBox(height: AppDimensions.padding20),
          _buildHintText(),
          const SizedBox(height: AppDimensions.padding20),
          _buildContactsPreview(),
        ],
      ),
      actionButton: IaFloatingActionButton(
        isEnabled: true,
        text: 'Erişim İste',
        onPressed: _requestContactsPermission,
      ),
    );
  }

  Widget _buildHintText() {
    return const Text(
      'Kişi listenize erişim sağlayarak arkadaşlarınızı kolayca ekleyin.',
      style: TextStyle(
        fontSize: 14,
        color: Colors.grey,
      ),
      maxLines: null,
    );
  }

  Widget _buildContactsPreview() {
    return const Expanded(
      child: IaRoundedContainer(
        roundness: AppDimensions.radiusL,
        color: AppColors.mediumGrey,
      ),
    );
  }

  Future<void> _requestContactsPermission() async {
    try {
      final permission = await Permission.contacts.request();
      Get.toNamed(AuthPages.contactsPage, arguments: permission.isGranted);
    } catch (error) {
      // If permission request fails, proceed without access
      print('Contacts permission error: $error');
      Get.find<AuthSharedState>().accessToContacts = false;
      Get.toNamed(AuthPages.contactsPage);
    }
  }
}
