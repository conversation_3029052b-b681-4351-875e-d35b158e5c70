import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_floating_action_button.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/auth/constants/auth_dimensions.dart';
import 'package:ivent_app/features/auth/constants/strings.dart';
import 'package:ivent_app/features/auth/controllers/auth_registration_controller.dart';
import 'package:ivent_app/features/auth/widgets/auth_info_text_widget.dart';
import 'package:ivent_app/features/auth/widgets/name_input_widget.dart';

class NamePage extends GetView<AuthRegistrationController> {
  const NamePage({super.key});

  @override
  Widget build(BuildContext context) {
    return IaScaffold.auth(
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Spacer(),
          const AuthInfoTextWidget(text: AuthStrings.tanisalim),
          const SizedBox(height: AuthDimensions.sectionSpacing),
          NameInputWidget(
            controller: controller.fullnameTextController,
            onValidationChanged: controller.handleFullnameValidationChanged,
            onChanged: controller.handleFullnameChanged,
          ),
          const Spacer(),
          Obx(() => IaFloatingActionButton(
            isEnabled: controller.canContinueToHobbiesPage,
            text: AuthStrings.devamEt,
            onPressed: controller.goToHobbiesPage,
          )),
        ],
      ),
    );
  }
}
