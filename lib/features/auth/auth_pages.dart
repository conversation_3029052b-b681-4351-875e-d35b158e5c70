import 'package:get/get.dart';
import 'package:ivent_app/core/middlewares/auth_middleware.dart';
import 'package:ivent_app/features/auth/auth_bindings.dart';
import 'package:ivent_app/features/auth/views/access_page.dart';
import 'package:ivent_app/features/auth/views/contacts_page.dart';
import 'package:ivent_app/features/auth/views/name_page.dart';
import 'package:ivent_app/features/auth/views/onboarding.dart';
import 'package:ivent_app/features/auth/views/phone_page.dart';
import 'package:ivent_app/features/auth/views/registration_hobbies_page.dart';
import 'package:ivent_app/features/auth/views/validate_phone.dart';

class AuthPages {
  AuthPages._();

  static const _prefix = '/auth';

  static const onboarding = '$_prefix/onboarding';
  static const phonePage = '$_prefix/phonePage';
  static const validatePhone = '$_prefix/validatePhone';
  static const namePage = '$_prefix/namePage';
  static const registrationHobbiesView = '$_prefix/registrationHobbiesView';
  static const accessPage = '$_prefix/accessPage';
  static const contactsPage = '$_prefix/contactsPage';

  static final routes = [
    GetPage(
      name: onboarding,
      page: () => const OnboardingPage(),
      binding: AuthBindings(),
      middlewares: [NoAuthMiddleware()],
    ),
    GetPage(name: phonePage, page: () => const PhonePage()),
    GetPage(name: validatePhone, page: () => const ValidatePhone()),
    GetPage(name: namePage, page: () => const NamePage()),
    GetPage(name: registrationHobbiesView, page: () => const RegistrationHobbiesPage()),
    GetPage(name: accessPage, page: () => const AccessPage()),
    GetPage(name: contactsPage, page: () => const ContactsPage()),
  ];
}
