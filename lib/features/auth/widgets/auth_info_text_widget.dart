import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';

class AuthInfoTextWidget extends StatelessWidget {
  final String text;
  final TextStyle? textStyle;
  final TextAlign textAlign;
  final int? maxLines;
  final EdgeInsetsGeometry? padding;

  const AuthInfoTextWidget({
    super.key,
    required this.text,
    this.textStyle,
    this.textAlign = TextAlign.center,
    this.maxLines,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    Widget textWidget = Text(
      text,
      style: textStyle ?? AppTextStyles.size24BoldPrimary,
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: maxLines != null ? TextOverflow.ellipsis : null,
    );

    if (padding != null) {
      textWidget = Padding(padding: padding!, child: textWidget);
    }

    return textWidget;
  }
}
