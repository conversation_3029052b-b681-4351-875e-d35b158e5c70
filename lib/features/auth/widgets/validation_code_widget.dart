import 'package:flutter/material.dart';
import 'package:flutter_verification_code/flutter_verification_code.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/features/auth/constants/validation_constants.dart';

class ValidationCodeWidget extends StatefulWidget {
  final ValueChanged<String> onCompleted;
  final ValueChanged<bool> onEditingChanged;
  final ValueChanged<bool>? onValidationChanged;

  const ValidationCodeWidget({
    super.key,
    required this.onCompleted,
    required this.onEditingChanged,
    this.onValidationChanged,
  });

  @override
  State<ValidationCodeWidget> createState() => _ValidationCodeWidgetState();
}

class _ValidationCodeWidgetState extends State<ValidationCodeWidget> {
  void _handleCodeCompleted(String code) {
    final bool isValid = code.length == AuthValidationConstants.validationCodeLength;

    widget.onValidationChanged?.call(isValid);
    widget.onCompleted(code);
  }

  void _handleEditingChanged(bool isEditing) {
    if (isEditing) {
      widget.onValidationChanged?.call(false);
    }

    if (!isEditing) {
      FocusScope.of(context).unfocus();
    }

    widget.onEditingChanged(isEditing);
  }

  @override
  Widget build(BuildContext context) {
    return VerificationCode(
      textStyle: AppTextStyles.size16Medium,
      keyboardType: TextInputType.number,
      digitsOnly: AuthValidationConstants.validationCodeDigitsOnly,
      underlineColor: AppColors.transparent,
      length: AuthValidationConstants.validationCodeLength,
      cursorColor: AppColors.primary,
      fullBorder: false,
      fillColor: AppColors.grey300,
      underlineUnfocusedColor: AppColors.transparent,
      onCompleted: _handleCodeCompleted,
      onEditing: _handleEditingChanged,
    );
  }
}
