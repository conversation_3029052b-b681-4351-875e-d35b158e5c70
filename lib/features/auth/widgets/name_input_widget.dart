import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/foundation/containers/ia_rounded_container.dart';
import 'package:ivent_app/features/auth/constants/strings.dart';
import 'package:ivent_app/features/auth/constants/validation_constants.dart';

class NameInputWidget extends StatefulWidget {
  final TextEditingController controller;
  final ValueChanged<bool> onValidationChanged;
  final ValueChanged<String>? onChanged;
  final double? width;

  const NameInputWidget({
    super.key,
    required this.controller,
    required this.onValidationChanged,
    this.onChanged,
    this.width,
  });

  @override
  State<NameInputWidget> createState() => _NameInputWidgetState();
}

class _NameInputWidgetState extends State<NameInputWidget> {
  void _handleTextChange(String text) {
    final bool isValid = text.trim().length >= AuthValidationConstants.fullNameMinLength;

    if (text.length > AuthValidationConstants.fullNameMaxLength) {
      widget.controller.value = widget.controller.value.copyWith(
        text: text.substring(0, AuthValidationConstants.fullNameMaxLength),
        selection: const TextSelection.collapsed(
          offset: AuthValidationConstants.fullNameMaxLength,
        ),
      );
      return;
    }

    widget.onValidationChanged(isValid);
    widget.onChanged?.call(text);
  }

  @override
  Widget build(BuildContext context) {
    return IaRoundedContainer(
      width: widget.width ?? 300.0,
      child: TextFormField(
        keyboardType: TextInputType.name,
        cursorHeight: 0,
        textAlign: TextAlign.center,
        inputFormatters: [
          FilteringTextInputFormatter.allow(
            RegExp(AuthValidationConstants.namePattern),
          ),
        ],
        controller: widget.controller,
        onChanged: _handleTextChange,
        decoration: InputDecoration(
          hintText: AuthStrings.adSoyad,
          hintStyle: AppTextStyles.size32BoldTextSecondary,
          border: InputBorder.none,
          focusedBorder: InputBorder.none,
        ),
        style: AppTextStyles.size32Bold,
      ),
    );
  }
}
