import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/cache/cache_manager.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/auth/auth_pages.dart';
import 'package:ivent_app/features/auth/controllers/auth_shared_state.dart';
import 'package:ivent_app/routes/app_pages.dart';

class AuthValidationController extends BaseController<AuthSharedState> {
  AuthValidationController(AuthService authService, AuthSharedState state) : super(authService, state);

  final _validationCode = ''.obs;
  final _isEditing = false.obs;
  final _canContinueToNextPage = false.obs;

  String get validationCode => _validationCode.value;
  bool get isEditing => _isEditing.value;
  bool get canContinueToNextPage => _canContinueToNextPage.value;
  bool get isValidationCodeComplete => validationCode.length == 6;

  void handleValidationCodeChanged(String code) => _validationCode.value = code;
  void handleValidationCodeEditingChanged(bool isEditing) => this._isEditing.value = isEditing;
  void handleValidationCodeValidationChanged(bool isValid) => _canContinueToNextPage.value = isValid;

  Future<void> validateUser() async {
    await runSafe(tag: 'validateUser', () async {
      final result = await authApi.validate(
        ValidateDto(
          validationCode: validationCode,
          phoneNumber: state.formattedPhoneNumber,
        ),
      );

      if (result == null) {
        goToSomethingWentWrongPage();
        return;
      }

      if (result.type == AuthEnum.login) {
        await authService.login(SessionUser(
          token: result.token!,
          sessionId: result.userId!,
          sessionRole: result.role!,
          sessionUsername: result.username!,
          sessionFullname: result.fullname!,
          sessionAvatarUrl: result.avatarUrl,
        ));
        Get.toNamed(AppPages.appNavigation);
      } else {
        Get.toNamed(AuthPages.namePage);
      }
    });
  }
}
