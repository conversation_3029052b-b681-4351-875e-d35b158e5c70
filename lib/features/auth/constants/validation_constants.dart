class AuthValidationConstants {
  AuthValidationConstants._();

  static const int phoneNumberMaxLength = 10;

  static const int phoneNumberMinLength = 10;

  static const String turkeyCountryCode = '+90';

  static const String phoneNumberPlaceholder = 'XXXXXXXXX';

  static const int validationCodeLength = 6;

  static const bool validationCodeDigitsOnly = true;

  static const int fullNameMinLength = 2;

  static const int fullNameMaxLength = 50;

  static const int minRequiredHobbies = 2;

  static const int maxInitialHobbiesDisplayed = 5;

  static const String numericOnlyPattern = r'[0-9]';

  static const String namePattern = r'^[a-zA-ZğüşıöçĞÜŞİÖÇ\s]+$';
}
