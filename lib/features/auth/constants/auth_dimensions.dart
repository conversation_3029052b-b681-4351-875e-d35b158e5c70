class AuthDimensions {
  AuthDimensions._();

  static const double phoneInputWidth = 198.0;

  static const double phoneInputHeight = 50.0;

  static const double validationCodeBoxSize = 50.0;

  static const double validationCodeSpacing = 8.0;

  static const double pageIndicatorRadius = 3.0;

  static const double pageIndicatorSpacing = 5.0;

  static const double selectedHobbyTagsHeight = 40.0;

  static const double hobbyTagSpacing = 8.0;

  static const double hobbyTagRowSpacing = 8.0;

  static const double hobbyCategorySpacing = 40.0;

  static const double formElementSpacing = 20.0;

  static const double sectionSpacing = 40.0;

  static const double relatedElementSpacing = 10.0;

  static const double standardButtonHeight = 50.0;

  static const double floatingButtonBottomMargin = 20.0;
}
