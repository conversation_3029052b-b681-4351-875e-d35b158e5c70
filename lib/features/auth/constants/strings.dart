class AuthStrings {
  AuthStrings._();

  static const String atla = 'Atla';

  static const String devamEt = 'Devam Et';

  static const String uyeligiTamamla = 'Üyeliği Tamamla';

  static const String hemenBaslat = 'Hemen Başlat!';

  static const String davetEt = 'Davet Et';

  static const String tekrarGonder = 'Tekrar Gönder';

  static const String iptal = 'İptal';

  static const String dahaFazlaGoster = 'Daha Fazla Göster';

  static const String dahaAzGoster = 'Daha Az Göster';

  static const String istekGonderildi = 'İstek Gönderildi';

  static const String alanCode = '+90';

  static const String adSoyad = 'Adınız Soyadınız';

  static const String ilgiAlani = 'En Az 2 İlgi Alanı';

  static const String kisileriniz = 'Kişileriniz';

  static const String arama = 'Arama';

  static const String iletisimNumGirerek = 'İletişim numaranı girerek\nseçkin etkinliklere katılabilirsin';

  static const String koduGirebilirsin = 'İletişim numarana gönderdiğimiz\nkodu aşağıya girebilirsin.';

  static const String tanisalim = 'Etkinliklere göz atmadan\nönce tanışalım';

  static const String ilgiAlaniText =
      'İlgi alanlarınızı belirleyerek gitmek isteyebileceğiniz etkinlikleri sizin için listeleyeceğiz.';

  static const String erisimIzin = 'Kişilerinize erişebilmemiz için ayarlar zart zurt...';

  static const String onayKoduHata =
      'Girdiğiniz kod mail adresinize gönderilen\nkod ile eşleşmedi. Lütfen tekrar kontrol ediniz.';

  static const List<String> onboarding1 = [
    'Tüm',
    'Etkinlikler',
    'Tek',
    'Haritada!',
  ];

  static const List<String> onboarding2 = ['Etkinlik', 'Keşfetmek', 'Çok', 'Kolay!'];

  static const List<String> onboarding3 = ['Arkadaşlarınla', 'Birlikte', 'Etkinliklere', 'Katıl!'];
}
