import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/widgets/composite/buttons/navigation_buttons.dart';
import 'package:ivent_app/core/widgets/foundation/containers/ia_rounded_container.dart';
import 'package:ivent_app/features/app_navigation/controllers/app_navigation_controller.dart';

class IaBottomNavigationBar extends GetView<AppNavigationController> {
  final List<String> iconPaths;
  final double height;

  const IaBottomNavigationBar({
    super.key,
    required this.iconPaths,
    this.height = AppDimensions.bottomNavigationBarHeight,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return controller.isNavBarVisible
          ? IaRoundedContainer(
              height: height,
              color: AppColors.white,
              boxShadow: AppColors.usualShadow,
              child: Row(
                children: iconPaths.asMap().entries.map((entry) {
                  final index = entry.key;
                  final iconPath = entry.value;
                  return Expanded(
                    child: NavigationButtons.navigationBar(
                      onTap: () => controller.changeTab(index),
                      iconPath: iconPath,
                      isActive: controller.currentIndex == index,
                    ),
                  );
                }).toList(),
              ),
            )
          : const SizedBox.shrink();
    });
  }
}
