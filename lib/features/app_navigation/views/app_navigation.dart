import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/app_navigation/controllers/app_navigation_controller.dart';
import 'package:ivent_app/features/home/<USER>';
import 'package:ivent_app/features/profile/profile_pages.dart';
import 'package:ivent_app/routes/app_pages.dart';
import 'package:ivent_app/routes/notifications.dart';
import 'package:ivent_app/shared/views/root_page.dart';

class AppNavigation extends GetView<AppNavigationController> {
  const AppNavigation({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final currentIndex = controller.currentIndex;
      return IndexedStack(
        index: currentIndex,
        children: [
          const _Navigator(
            index: 0,
            initialRoute: HomePages.homePage,
          ),
          const _Navigator(
            index: 1,
            initialRoute: NotificationsPages.notifications,
          ),
          _Navigator(
            index: 2,
            initialRoute: ProfilePages.userProfile,
            parameters: {'id': Get.find<AuthService>().sessionUser!.sessionId},
          ),
        ],
      );
    });
  }
}

class _Navigator extends StatelessWidget {
  final int index;
  final String initialRoute;
  final Map<String, String>? parameters;

  const _Navigator({
    required this.index,
    required this.initialRoute,
    this.parameters,
  });

  @override
  Widget build(BuildContext context) {
    return Navigator(
      key: Get.nestedKey(index),
      initialRoute: initialRoute,
      onGenerateInitialRoutes: _generateInitialRoutes,
      onGenerateRoute: _generateRoute,
    );
  }

  GetPage _getPage(String? routeName) {
    return AppPages.routes.firstWhere(
      (p) => p.name == routeName,
      orElse: () => AppPages.notFoundPage,
    );
  }

  List<Route> _generateInitialRoutes(NavigatorState navigator, String initialRouteName) {
    return [
      MaterialPageRoute(
        builder: (_) => RootPage(
          routeName: initialRouteName,
          index: index,
          parameters: parameters,
        ),
      ),
    ];
  }

  Route _generateRoute(RouteSettings settings) {
    final page = _getPage(settings.name);
    return GetPageRoute(
      settings: settings,
      page: page.page,
      binding: page.binding,
    );
  }
}
