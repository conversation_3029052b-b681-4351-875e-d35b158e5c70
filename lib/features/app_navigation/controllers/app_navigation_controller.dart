import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:ivent_app/features/home/<USER>';
import 'package:ivent_app/features/profile/profile_pages.dart';
import 'package:ivent_app/routes/notifications.dart';

class AppNavigationController extends GetxController {
  final _currentIndex = 0.obs;
  final _drawerVisibility = <int, bool>{0: true, 1: true, 2: true}.obs;
  final _navBarVisibility = <int, bool>{0: true, 1: true, 2: true}.obs;

  int get currentIndex => _currentIndex.value;
  bool get isDrawerVisible => _drawerVisibility[currentIndex] ?? true;
  bool get isNavBarVisible => _navBarVisibility[currentIndex] ?? true;

  final tabs = [
    HomePages.homePage,
    NotificationsPages.notifications,
    ProfilePages.userProfile,
  ];

  final cachedNavigators = <int, Widget>{};

  final drawerVisibilityDefaults = {0: true, 1: true, 2: true};
  final navBarVisibilityDefaults = {0: true, 1: true, 2: true};

  void changeTab(int index) {
    if (index == currentIndex) {
      debugPrint(' [NAVIGATION] Redirecting to ${tabs[index]}...');
      Get.offAllNamed(tabs[index], id: index);
      setDrawerVisibility(index, drawerVisibilityDefaults[index] ?? true);
      setNavBarVisibility(index, navBarVisibilityDefaults[index] ?? true);
    }
    _currentIndex.value = index;
  }

  void setDrawerVisibility(int index, bool isVisible) {
    _drawerVisibility[index] = isVisible;
  }

  void setNavBarVisibility(int index, bool isVisible) {
    _navBarVisibility[index] = isVisible;
  }

  void showDrawer() => _drawerVisibility[currentIndex] = true;
  void hideDrawer() => _drawerVisibility[currentIndex] = false;
  void showNavBar() => _navBarVisibility[currentIndex] = true;
  void hideNavBar() => _navBarVisibility[currentIndex] = false;
}
