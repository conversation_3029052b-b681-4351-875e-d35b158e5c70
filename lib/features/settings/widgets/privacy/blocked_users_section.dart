import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/features/settings/constants/settings_constants.dart';
import 'package:ivent_app/features/settings/controllers/privacy_settings_controller.dart';
import 'package:ivent_app/features/settings/widgets/ia_settings_list_tile.dart';
import 'package:ivent_app/routes/settings.dart';

class BlockedUsersSection extends StatelessWidget {
  final PrivacySettingsController controller;

  const BlockedUsersSection({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Obx(() => IaSettingsListTile(
          icon: AppAssets.users,
          title: SettingsConstants.blockedUsersItem,
          onTap: () => Get.toNamed(SettingsPages.blockedUsers),
          trailing: controller.isLoadingBlocked.value
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : null,
        ));
  }
}
