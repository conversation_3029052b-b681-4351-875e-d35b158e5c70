import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/features/settings/constants/faq_constants.dart';
import 'package:ivent_app/features/settings/utils/settings_dialogs.dart';
import 'package:ivent_app/features/settings/widgets/ia_settings_list_tile.dart';

class FaqSection extends StatelessWidget {
  const FaqSection({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: FaqConstants.faqItems
          .map((faq) => Padding(
                padding: const EdgeInsets.only(bottom: AppDimensions.padding12),
                child: IaSettingsListTile(
                  icon: AppAssets.help,
                  title: faq.title,
                  onTap: () => SettingsDialogs.showFaqDialog(faq.title, faq.content),
                ),
              ))
          .toList(),
    );
  }
}
