import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_colors.dart';

/// Settings divider widget
///
/// A custom divider specifically designed for settings pages.
/// Uses grey400 color with 0.4 opacity and 1px height.
class SettingsDivider extends StatelessWidget {
  final EdgeInsetsGeometry? margin;

  const SettingsDivider({
    super.key,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      width: double.maxFinite,
      height: 1,
      color: AppColors.grey400.withValues(alpha: 0.4),
    );
  }
}
