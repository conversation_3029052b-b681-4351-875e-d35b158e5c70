import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/features/settings/constants/settings_constants.dart';
import 'package:ivent_app/features/settings/controllers/settings_controller.dart';
import 'package:ivent_app/features/settings/widgets/common/settings_divider.dart';
import 'package:ivent_app/features/settings/widgets/ia_settings_list_tile.dart';

class SettingsMenuSection extends StatelessWidget {
  final SettingsController controller;

  const SettingsMenuSection({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        IaSettingsListTile(
          icon: AppAssets.lockOpen,
          title: SettingsConstants.privacySettingsItem,
          onTap: controller.goToPrivacySettings,
        ),
        const SizedBox(height: AppDimensions.padding16),
        const SettingsDivider(),
        /* THIS WIDGET TEMPORARILY REMOVED FOR MVP. WILL BE ADDED BACK SOON.
        IaSettingsListTile(
          icon: AppAssets.lock,
          title: SettingsConstants.securitySettingsItem,
          subtitle: SettingsConstants.securitySettingsSubtitle,
          onTap: controller.goToSecuritySettings,
        ),
        */
        const SizedBox(height: AppDimensions.padding16),
        IaSettingsListTile(
          icon: AppAssets.info,
          title: SettingsConstants.aboutItem,
          onTap: controller.goToAboutPage,
        ),
        const SizedBox(height: AppDimensions.padding16),
        IaSettingsListTile(
          icon: AppAssets.headphones,
          title: SettingsConstants.supportItem,
          onTap: controller.goToSupportPage,
        ),     

        const SizedBox(height: AppDimensions.padding16),
        const SettingsDivider(),
        const SizedBox(height: AppDimensions.padding16),

        IaSettingsListTile(
          icon: AppAssets.logOut,
          title: SettingsConstants.logoutItem,
          onTap: controller.showLogoutDialog,
          titleColor: AppColors.warning,
          iconColor: AppColors.destructive,
          iconBgColor: AppColors.destructive.withValues(alpha: 0.1),
        ),
        /* THIS WIDGET TEMPORARILY REMOVED FOR MVP. WILL BE ADDED BACK SOON.
        const SizedBox(height: AppDimensions.padding8),
        IaSettingsListTile(
          icon: AppAssets.deleteColumn,
          title: SettingsConstants.deleteAccountItem,
          onTap: controller.showDeleteAccountDialog,
          titleColor: AppColors.error,
          iconColor: AppColors.destructive,
          iconBgColor: AppColors.destructive.withValues(alpha: 0.1),
        ),
        */
      ],
    );
  }
}
