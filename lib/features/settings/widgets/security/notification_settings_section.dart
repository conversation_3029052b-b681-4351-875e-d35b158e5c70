import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/features/settings/constants/settings_constants.dart';
import 'package:ivent_app/features/settings/controllers/security_settings_controller.dart';
import 'package:ivent_app/features/settings/widgets/ia_settings_list_tile.dart';

class NotificationSettingsSection extends StatelessWidget {
  final SecuritySettingsController controller;

  const NotificationSettingsSection({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Obx(() => IaSettingsListTile(
              icon: AppAssets.bellNotification,
              title: SettingsConstants.pushNotificationsItem,
              trailing: Switch(
                value: controller.pushNotifications.value,
                onChanged: (value) {
                  controller.pushNotifications.value = value;
                  controller.updateNotificationSettings();
                },
                activeColor: AppColors.primary,
              ),
            )),
        const SizedBox(height: 8),
        Obx(() => IaSettingsListTile(
              icon: AppAssets.mail,
              title: SettingsConstants.emailNotificationsItem,
              trailing: Switch(
                value: controller.emailNotifications.value,
                onChanged: (value) {
                  controller.emailNotifications.value = value;
                  controller.updateNotificationSettings();
                },
                activeColor: AppColors.primary,
              ),
            )),
        const SizedBox(height: 8),
        Obx(() => IaSettingsListTile(
              icon: AppAssets.phone,
              title: SettingsConstants.smsNotificationsItem,
              trailing: Switch(
                value: controller.smsNotifications.value,
                onChanged: (value) {
                  controller.smsNotifications.value = value;
                  controller.updateNotificationSettings();
                },
                activeColor: AppColors.primary,
              ),
            )),
        const SizedBox(height: 8),
        Obx(() => IaSettingsListTile(
              icon: AppAssets.alarm,
              title: SettingsConstants.eventRemindersItem,
              trailing: Switch(
                value: controller.eventReminders.value,
                onChanged: (value) {
                  controller.eventReminders.value = value;
                  controller.updateNotificationSettings();
                },
                activeColor: AppColors.primary,
              ),
            )),
        const SizedBox(height: 8),
        Obx(() => IaSettingsListTile(
              icon: AppAssets.usersGroup,
              title: SettingsConstants.socialNotificationsItem,
              trailing: Switch(
                value: controller.socialNotifications.value,
                onChanged: (value) {
                  controller.socialNotifications.value = value;
                  controller.updateNotificationSettings();
                },
                activeColor: AppColors.primary,
              ),
            )),
        const SizedBox(height: 8),
        Obx(() => IaSettingsListTile(
              icon: AppAssets.mailOpen,
              title: SettingsConstants.marketingEmailsItem,
              trailing: Switch(
                value: controller.marketingEmails.value,
                onChanged: (value) {
                  controller.marketingEmails.value = value;
                  controller.updateNotificationSettings();
                },
                activeColor: AppColors.primary,
              ),
            )),
      ],
    );
  }
}
