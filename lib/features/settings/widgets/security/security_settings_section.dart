import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/features/settings/constants/settings_constants.dart';
import 'package:ivent_app/features/settings/controllers/security_settings_controller.dart';
import 'package:ivent_app/features/settings/utils/settings_dialogs.dart';
import 'package:ivent_app/features/settings/widgets/ia_settings_list_tile.dart';

class SecuritySettingsSection extends StatelessWidget {
  final SecuritySettingsController controller;

  const SecuritySettingsSection({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Obx(() => IaSettingsListTile(
              icon: AppAssets.shieldCheck,
              title: SettingsConstants.twoFactorAuthItem,
              trailing: Switch(
                value: controller.twoFactorAuth.value,
                onChanged: (value) => controller.updateSecuritySetting('twoFactorAuth', value),
                activeColor: AppColors.primary,
              ),
            )),
        const SizedBox(height: 8),
        Obx(() => IaSettingsListTile(
              icon: AppAssets.info,
              title: SettingsConstants.loginAlertsItem,
              trailing: Switch(
                value: controller.loginAlerts.value,
                onChanged: (value) => controller.updateSecuritySetting('loginAlerts', value),
                activeColor: AppColors.primary,
              ),
            )),
        const SizedBox(height: AppDimensions.padding20),
        IaSettingsListTile(
          icon: AppAssets.circleWarning,
          title: SettingsConstants.deleteAccountItem,
          titleColor: Colors.red,
          onTap: () => SettingsDialogs.showDeleteAccountDialog(context),
        ),
      ],
    );
  }
}
