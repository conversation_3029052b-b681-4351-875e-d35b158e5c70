class SettingsConstants {
  static const String settingsTitle = 'Ayarlar';
  static const String privacySettingsTitle = 'Gizlilik';
  static const String securitySettingsTitle = 'Güvenlik';
  static const String supportTitle = 'Destek';
  static const String aboutTitle = 'Hakkında';
  static const String blockedUsersTitle = 'Engellenen Kişiler';
  static const String pdfViewerTitle = 'PDF Görüntüleyici';

  static const String privacySettingsItem = 'Gizlilik';
  static const String securitySettingsItem = 'Güvenlik Ayarları';
  static const String supportItem = 'Destek';
  static const String aboutItem = 'Hakkında';
  static const String logoutItem = 'Çıkış Yap';
  static const String deleteAccountItem = 'Hesabı Sil';

  static const String blockedUsersItem = 'Engellenen Kişiler';

  static const String privacyInfoTitle = 'Gizlilik Bilgisi';
  static const String privacyInfoContent = 'Şu anda sadece engellenen kullanıcılar listesi backend ile entegre çalışmaktadır. '
      'Diğer gizlilik ayarları backend API\'leri hazır olduğunda eklenecektir.';

  static const String noBlockedUsersMessage = 'Engellenen kullanıcı yok';
  static const String noBlockedUsersDescription = 'Henüz hiç kimseyi engellemediniz.';
  static const String unblockButtonText = 'Engeli Kaldır';
  static const String unblockDialogTitle = 'Engeli Kaldır';
  static const String unblockDialogContent = ' kullanıcısının engelini kaldırmak istediğinizden emin misiniz?';
  static const String cancelButtonText = 'İptal';

  static const String emailUpdateItem = 'E-posta Adresi Güncelle';
  static const String phoneUpdateItem = 'Telefon Numarası Güncelle';
  static const String emailVerificationItem = 'E-posta Doğrulama Kodu Gönder';

  static const String educationStatusItem = 'Eğitim Durumu';
  static const String pushNotificationsItem = 'Push Bildirimleri';
  static const String emailNotificationsItem = 'E-posta Bildirimleri';
  static const String smsNotificationsItem = 'SMS Bildirimleri';
  static const String eventRemindersItem = 'Etkinlik Hatırlatıcıları';
  static const String socialNotificationsItem = 'Sosyal Bildirimler';
  static const String marketingEmailsItem = 'Pazarlama E-postaları';

  static const String twoFactorAuthItem = 'İki Faktörlü Doğrulama';
  static const String loginAlertsItem = 'Giriş Uyarıları';

  static const String deleteAccountDialogTitle = 'Hesabı Sil';
  static const String deleteAccountDialogContent = 'Hesabınızı silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.';
  static const String deleteButtonText = 'Sil';
  static const String accountDeletionComingSoon = 'Hesap silme özelliği yakında aktif olacak.';

  static const String contactItem = 'İletişim';

  static const String helpInfoTitle = 'Yardım';
  static const String helpInfoContent = 'Başka sorularınız varsa "İletişim" bölümünden bizimle iletişime geçebilirsiniz. '
      'Size en kısa sürede yardımcı olmaya çalışacağız.';

  static const String appName = 'iVent';
  static const String appVersion = 'Sürüm 1.0.0';
  static const String appDescription = 'Hepimizin şehrin içinde şehirden uzak hissettiği anlar olmuştur. '
      'Henüz varlığını bile bilmediğin nice tecrübeler çok yakınında. '
      'Ve iVent App\'de bunları keşfedeceksin!';

  static const String privacyPolicyItem = 'Gizlilik Politikası';
  static const String termsOfServiceItem = 'Kullanım Şartları';
  static const String communityRulesItem = 'Topluluk Kuralları';
  static const String websiteItem = 'Web Sitesi';

  static const String copyrightText = '© 2025 iVent App';
  static const String rightsReservedText = 'Tüm hakları saklıdır.';

  static const String communityRulesDialogTitle = 'Topluluk Kuralları';
  static const String communityRulesIntro = 'iVent topluluğunda güvenli ve saygılı bir ortam için:';
  static const String communityRulesWarning = 'Bu kurallara uymayan kullanıcılar uyarı alabilir veya hesapları kapatılabilir.';
  static const String understoodButtonText = 'Anladım';

  // External URLs
  static const String websiteUrl = 'https://ivent.app/';
  static const String privacyPolicyUrl = 'https://ivent.app/I%20Vent%20Gizlilik%20Politikas%C4%B1.pdf';
  static const String termsOfServiceUrl = 'https://ivent.app/I%20Vent%20Kullan%C4%B1m%20%C5%9Eartlar%C4%B1.pdf';

  static const String pdfLoadingText = 'PDF yükleniyor...';
  static const String pdfNotFoundText = 'PDF bulunamadı';
  static const String pdfLoadErrorText = 'PDF yüklenemedi: ';
  static const String pdfLoadingErrorText = 'PDF yüklenirken hata oluştu: ';
  static const String pdfDisplayErrorText = 'PDF görüntülenirken hata oluştu';
  static const String retryButtonText = 'Tekrar Dene';

  static const Map<String, String> educationStatusMap = {
    'unverified': 'Doğrulanmadı',
    'student': 'Öğrenci',
    'grad': 'Mezun',
    'verified': 'Doğrulandı',
  };

  static String getEducationStatusText(String status) {
    return educationStatusMap[status] ?? 'Bilinmiyor';
  }
}
