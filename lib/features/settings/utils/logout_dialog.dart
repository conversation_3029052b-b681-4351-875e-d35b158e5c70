import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/services/auth_service.dart';

class LogoutDialog {
  static void show() {
    Get.dialog(
      AlertDialog(
        backgroundColor: AppColors.background,
        title: Text('Çıkış Yap', style: AppTextStyles.size16Bold),
        content: Text(
          'Çıkış yapmak istediğinizden emin misiniz?',
          style: AppTextStyles.size14Regular,
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('İptal', style: AppTextStyles.size14Medium),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              Get.find<AuthService>().logout();
            },
            child: Text(
              '<PERSON>ık<PERSON><PERSON> Yap',
              style: AppTextStyles.size14Bold.copyWith(color: AppColors.error),
            ),
          ),
        ],
      ),
    );
  }
}
