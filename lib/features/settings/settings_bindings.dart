import 'package:get/get.dart';
import 'package:ivent_app/features/settings/controllers/settings_controller.dart';
import 'package:ivent_app/features/settings/controllers/settings_state_manager.dart';

class SettingsBindings implements Bindings {
  @override
  void dependencies() {
    // Register SettingsSharedState if not already registered
    if (!Get.isRegistered<SettingsSharedState>()) {
      Get.put(SettingsSharedState(), permanent: true);
    }
    
    // Register SettingsController if not already registered
    if (!Get.isRegistered<SettingsController>()) {
      Get.lazyPut(() => SettingsController(), fenix: true);
    }
  }
}
