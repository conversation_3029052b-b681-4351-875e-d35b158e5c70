import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/widgets/ia_search_results_builder.dart';
import 'package:ivent_app/core/widgets/index.dart';
import 'package:ivent_app/features/notifications/controllers/notification_controller.dart';
import 'package:ivent_app/features/notifications/widgets/notification_tile.dart';

class NotificationsPage extends GetView<NotificationController> {
  const NotificationsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Material(
      child: Obx(() => IaSearchResultsBuilder(
            entityName: 'Bildirim',
            isSearching: controller.isSearching,
            isResultsEmpty: controller.isResultsEmpty,
            isQueryEmpty: true,
            initialSearchBehavior: InitialSearchBehavior.loaded,
            builder: (context) {
              final notifications = controller.notifications!;
              return ListView.separated(
                itemCount: notifications.notificationCount,
                itemBuilder: (context, index) {
                  return NotificationTile(notification: notifications.notifications[index]);
                },
                separatorBuilder: (context, index) => const IaDivider(),
              );
            },
          )),
    );
  }
}
