import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/ia_builder.dart';
import 'package:ivent_app/features/profile/controllers/profile_vibes_controller.dart';
import 'package:ivent_app/features/profile/widgets/user_profile/vibe_thumbnail.dart';

class ContentTabs extends StatelessWidget {
  final String userId;

  const ContentTabs({super.key, required this.userId});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const _TabControls(),
        Expanded(
          child: Padding(
            padding: const EdgeInsets.only(top: AppDimensions.padding20, bottom: 100),
            child: TabBarView(
              physics: const NeverScrollableScrollPhysics(),
              children: [
                _VibeTab(userId: userId),
                const _MemoriesTab(),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

class _TabControls extends StatelessWidget {
  const _TabControls();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: AppDimensions.padding20),
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
        height: 40,
        child: TabBar(
          dividerHeight: 0,
          indicatorColor: Colors.transparent,
          indicatorSize: TabBarIndicatorSize.tab,
          overlayColor: WidgetStateProperty.all(Colors.transparent),
          labelStyle: AppTextStyles.size14Bold,
          unselectedLabelStyle: AppTextStyles.size14BoldTextSecondary,
          tabs: const [Tab(text: 'Vibes'), Tab(text: 'iVent Memories')],
        ),
      ),
    );
  }
}

class _VibeTab extends GetView<ProfileVibesController> {
  final String userId;

  const _VibeTab({required this.userId});

  @override
  String? get tag => userId;

  GetVibeFoldersByUserIdReturn? get vibesContent => controller.vibeFoldersReturn;

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return IaBuilder(
        isLoading: controller.isLoading('loadFolders'),
        isEmpty: vibesContent == null || vibesContent!.vibeFolders.isEmpty,
        builder: (context) {
          return GridView.builder(
            padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: AppDimensions.padding16,
              mainAxisSpacing: AppDimensions.padding16,
              childAspectRatio: 0.6,
            ),
            itemCount: _getVibesItemCount(),
            itemBuilder: (context, index) => _VibeItem(
              controller: controller,
              vibesContent: vibesContent,
              index: index,
            ),
          );
        },
      );
    });
  }

  int _getVibesItemCount() {
    final baseCount = vibesContent!.vibeFolders.length;
    return controller.authService.hasUpcomingEventToday ? baseCount + 1 : baseCount;
  }
}

class _VibeItem extends StatelessWidget {
  const _VibeItem({
    required this.controller,
    required this.vibesContent,
    required this.index,
  });

  final ProfileVibesController controller;
  final GetVibeFoldersByUserIdReturn? vibesContent;
  final int index;

  @override
  Widget build(BuildContext context) {
    if (controller.authService.hasUpcomingEventToday) {
      if (index == 0) {
        // TODO: Implement upcoming event display
        return const SizedBox.shrink();
      } else {
        final vibe = vibesContent!.vibeFolders[index - 1];
        return _VibeThumbnail(vibe: vibe);
      }
    } else {
      final vibe = vibesContent!.vibeFolders[index];
      return _VibeThumbnail(vibe: vibe);
    }
  }
}

class _VibeThumbnail extends StatelessWidget {
  const _VibeThumbnail({
    required this.vibe,
  });

  final dynamic vibe;

  @override
  Widget build(BuildContext context) {
    return IaVibeThumbnail(
      vibeId: vibe.vibeId,
      iventName: vibe.iventName,
      participantCount: vibe.memberCount,
      participantNames: vibe.memberFirstnames,
      imageUrl: vibe.thumbnailUrl,
    );
  }
}

class _MemoriesTab extends StatelessWidget {
  const _MemoriesTab();

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Text(
        'Memories yakın zamanda eklenecek...',
        style: AppTextStyles.size16RegularTextSecondary,
      ),
    );
  }
}
