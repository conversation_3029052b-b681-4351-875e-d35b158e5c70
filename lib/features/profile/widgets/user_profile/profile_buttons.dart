import 'package:flutter/material.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_rounded_button.dart';
import 'package:ivent_app/core/widgets/foundation/graphics/ia_svg_icon.dart';

class ProfileButtons extends StatelessWidget {
  final EdgeInsetsGeometry? margin;
  final VoidCallback? onTap;
  final bool isActive;
  final String text;
  final String? iconPath;

  const ProfileButtons._({
    super.key,
    required this.margin,
    required this.onTap,
    required this.isActive,
    required this.text,
    required this.iconPath,
  });

  @override
  Widget build(BuildContext context) {
    return IaRoundedButton(
      margin: margin,
      height: AppDimensions.buttonHeightProfile,
      roundness: AppDimensions.buttonRadiusS,
      onTap: onTap,
      color: isActive ? AppColors.primary : AppColors.grey400,
      text: text,
      textStyle: AppTextStyles.buttonTextMedium,
      leading: iconPath != null ? IaSvgIcon(iconPath: iconPath!) : null,
    );
  }

  static ProfileButtons profileFollowings({
    Key? key,
    EdgeInsetsGeometry? margin,
    required VoidCallback onTap,
  }) {
    return ProfileButtons._(
      key: key,
      margin: margin,
      onTap: onTap,
      isActive: true,
      text: 'Takip Ettiklerin',
      iconPath: AppAssets.wavyCheck,
    );
  }

  static ProfileButtons profileFavorites({
    Key? key,
    EdgeInsetsGeometry? margin,
    required VoidCallback onTap,
  }) {
    return ProfileButtons._(
      key: key,
      margin: margin,
      onTap: onTap,
      isActive: true,
      text: 'Favoriler',
      iconPath: AppAssets.star,
    );
  }

  static ProfileButtons profileAddFriend({
    Key? key,
    EdgeInsetsGeometry? margin,
    required VoidCallback onTap,
    required UserRelationshipStatusEnum? relationshipStatus,
  }) {
    return ProfileButtons._(
      key: key,
      margin: margin,
      onTap: onTap,
      isActive: relationshipStatus == null,
      text: relationshipStatus == UserRelationshipStatusEnum.accepted
          ? 'Arkadaşsınız'
          : relationshipStatus == UserRelationshipStatusEnum.pending
              ? 'İstek Gönderildi'
              : 'Arkadaş Ekle',
      iconPath: relationshipStatus == UserRelationshipStatusEnum.accepted
          ? AppAssets.users
          : relationshipStatus == UserRelationshipStatusEnum.pending
              ? null
              : AppAssets.userAdd,
    );
  }

  static ProfileButtons profileFollow({
    Key? key,
    EdgeInsetsGeometry? margin,
    required VoidCallback onTap,
    required bool isFollowing,
  }) {
    return ProfileButtons._(
      key: key,
      margin: margin,
      onTap: onTap,
      isActive: !isFollowing,
      text: isFollowing ? 'Takip Ediyorsun' : 'Takip Et',
      iconPath: isFollowing ? AppAssets.check : AppAssets.addPlus,
    );
  }

  static ProfileButtons profileMembers({
    Key? key,
    EdgeInsetsGeometry? margin,
    required VoidCallback onTap,
  }) {
    return ProfileButtons._(
      key: key,
      margin: margin,
      onTap: onTap,
      isActive: true,
      text: 'Üyeler',
      iconPath: AppAssets.usersGroup,
    );
  }
}
