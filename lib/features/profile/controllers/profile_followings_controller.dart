import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/core/widgets/ia_search_results_builder.dart';
import 'package:ivent_app/features/profile/controllers/profile_shared_state.dart';

class ProfileFollowingsController extends BaseControllerWithSearch<ProfileSharedState> {
  final _followingsResult = Rxn<GetFollowingsByUserIdReturn>();

  ProfileFollowingsController(AuthService authService, ProfileSharedState state) : super(authService, state);

  GetFollowingsByUserIdReturn? get followingsResult => _followingsResult.value;

  @override
  bool get isResultsEmpty => followingsResult?.followings.isEmpty ?? true;

  @override
  InitialSearchBehavior get initialSearchBehavior => InitialSearchBehavior.loaded;

  @override
  Future<void> onSearch([String? query]) async {
    _followingsResult.value = await usersApi.getFollowingsByUserId(
      state.userId,
      q: query,
    );
  }
}
