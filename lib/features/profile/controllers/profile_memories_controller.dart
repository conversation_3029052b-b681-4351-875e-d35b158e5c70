import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/profile/controllers/profile_shared_state.dart';

class ProfileMemoriesController extends BaseController<ProfileSharedState> {
  final _memoryFoldersReturn = Rxn<GetMemoryFoldersByUserIdReturn>();

  ProfileMemoriesController(AuthService authService, ProfileSharedState state) : super(authService, state);

  GetMemoryFoldersByUserIdReturn? get memoryFoldersReturn => _memoryFoldersReturn.value;

  @override
  void onInitAsync() async {
    super.onInitAsync();
    await _loadFolders();
  }

  Future<void> _loadFolders() async {
    await runSafe(tag: 'loadFolders', () async {
      final result = await usersApi.getMemoryFoldersByUserId(state.userId);
      _memoryFoldersReturn.value = result;
    });
  }
}
