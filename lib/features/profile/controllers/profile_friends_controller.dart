import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/core/widgets/ia_search_results_builder.dart';
import 'package:ivent_app/features/profile/controllers/profile_shared_state.dart';

class ProfileFriendsController extends BaseControllerWithSearch<ProfileSharedState> {
  final _friendsResult = Rxn<SearchFriendsByUserIdReturn>();

  ProfileFriendsController(AuthService authService, ProfileSharedState state) : super(authService, state);

  SearchFriendsByUserIdReturn? get friendsResult => _friendsResult.value;

  @override
  bool get isResultsEmpty => friendsResult?.friends.isEmpty ?? true;

  @override
  InitialSearchBehavior get initialSearchBehavior => InitialSearchBehavior.loaded;

  @override
  Future<void> onSearch([String? query]) async {
    _friendsResult.value = await userRelationshipsApi.searchFriendsByUserId(
      state.userId,
      FriendListingTypeEnum.user,
      q: query,
    );
  }
}
