import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/profile/controllers/profile_shared_state.dart';
import 'package:ivent_app/features/side_menu/utils/profile_dialogs.dart';

class ProfileUserInfoController extends BaseController<ProfileSharedState> {
  final _userPageInfo = Rxn<GetUserByUserIdReturn>();
  final _isFollowing = false.obs;
  final _relationshipStatus = Rxn<UserRelationshipStatusEnum>();

  GetUserByUserIdReturn? get userPageInfo => _userPageInfo.value;
  bool get isFollowing => _isFollowing.value;
  UserRelationshipStatusEnum? get relationshipStatus => _relationshipStatus.value;

  ProfileUserInfoController(AuthService authService, ProfileSharedState state) : super(authService, state);

  @override
  Future<void> onInitAsync() async {
    super.onInitAsync();
    await _loadUserInfo();
  }

  Future<void> _loadUserInfo() async {
    await runSafe(tag: 'loadUserInfo', () async {
      _userPageInfo.value = await usersApi.getByUserId(state.userId);
      _isFollowing.value = _userPageInfo.value!.isFollowing;
      _relationshipStatus.value = _userPageInfo.value!.relationshipStatus;
      state.userRole = _userPageInfo.value!.userRole;
    });
  }

  Future<void> toggleFollowing() async {
    await runSafe(tag: 'toggleFollowing', () async {
      if (isFollowing) {
        _isFollowing.value = false;
        await usersApi.unfollowByUserId(state.userId);
      } else {
        _isFollowing.value = true;
        await usersApi.followByUserId(state.userId);
      }
    });
  }

  Future<void> toggleFriendship() async {
    await runSafe(tag: 'toggleFriendship', () async {
      if (relationshipStatus == UserRelationshipStatusEnum.accepted) {
        _relationshipStatus.value = null;
        await userRelationshipsApi.removeFriendByUserId(state.userId);
      } else {
        _relationshipStatus.value = UserRelationshipStatusEnum.accepted;
        await userRelationshipsApi.inviteFriendByUserId(state.userId);
      }
    });
  }

  void openBlockSettings() {
    Get.bottomSheet(
      Container(
        decoration: const BoxDecoration(
          color: AppColors.background,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(AppDimensions.radiusL),
            topRight: Radius.circular(AppDimensions.radiusL),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header with close button
            Container(
              padding: const EdgeInsets.all(AppDimensions.padding16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'User Actions',
                    style: AppTextStyles.size16Bold,
                  ),
                  IconButton(
                    onPressed: () => Get.back(),
                    icon: const Icon(Icons.close, color: AppColors.darkGrey),
                  ),
                ],
              ),
            ),
            const Divider(height: 1, color: AppColors.lightGrey),

            // Action buttons list
            _buildActionButton(
              icon: Icons.block,
              iconColor: AppColors.error,
              title: relationshipStatus == UserRelationshipStatusEnum.blocked ? 'Unblock User' : 'Block User',
              subtitle: relationshipStatus == UserRelationshipStatusEnum.blocked
                  ? 'Allow this user to see your content'
                  : 'Prevent this user from seeing your content',
              onTap: _handleBlockAction,
            ),

            _buildActionButton(
              icon: Icons.report,
              iconColor: AppColors.warning,
              title: 'Report User',
              subtitle: 'Report inappropriate behavior',
              onTap: _handleReportAction,
            ),

            _buildActionButton(
              icon: Icons.visibility_off,
              iconColor: AppColors.darkGrey,
              title: 'Hide Posts',
              subtitle: 'Hide posts from this user in your feed',
              onTap: _handleHidePostsAction,
            ),

            const SizedBox(height: AppDimensions.padding16),
          ],
        ),
      ),
      isScrollControlled: true,
      isDismissible: true,
      enableDrag: true,
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required Color iconColor,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(AppDimensions.padding8),
        decoration: BoxDecoration(
          color: iconColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(AppDimensions.radiusS),
        ),
        child: Icon(
          icon,
          color: iconColor,
          size: 20,
        ),
      ),
      title: Text(
        title,
        style: AppTextStyles.size16Medium,
      ),
      subtitle: Text(
        subtitle,
        style: AppTextStyles.size14Regular.copyWith(
          color: AppColors.textSecondary,
        ),
      ),
      onTap: () {
        Get.back(); // Close bottom sheet first
        onTap(); // Then execute action
      },
      contentPadding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.padding16,
        vertical: AppDimensions.padding8,
      ),
    );
  }

  void _handleBlockAction() async {
    final bool isCurrentlyBlocked = relationshipStatus == UserRelationshipStatusEnum.blocked;
    final String actionTitle = isCurrentlyBlocked ? 'Kullanici Engelini Kaldır' : 'Kullanıcıyı Engelle';
    final String actionMessage = isCurrentlyBlocked
        ? 'Engeli kaldırmak istediğinizden emin misiniz? Bu kullanıcı artık sizin içeriklerinizi görebilecek.'
        : 'Bu kullanıcıyı engellemek istediğinizden emin misiniz? Bu kullanıcı artık sizin içeriklerinizi göremez.';

    ProfileDialogs.showConfirmationDialog(
      title: actionTitle,
      message: actionMessage,
      confirmText: isCurrentlyBlocked ? 'Engeli Kaldır' : 'Engelle',
      onConfirm: () async {
        await runSafe(tag: 'blockAction', () async {
          if (isCurrentlyBlocked) {
            await userRelationshipsApi.unblockUserByUserId(state.userId);
            _relationshipStatus.value = null;
            Get.snackbar(
              'Engel Kaldırıldı',
              'Kullanıcının engeli kaldırıldı.',
              backgroundColor: AppColors.success,
              colorText: AppColors.white,
              snackPosition: SnackPosition.TOP,
            );
          } else {
            await userRelationshipsApi.blockUserByUserId(state.userId);
            _relationshipStatus.value = UserRelationshipStatusEnum.blocked;
            Get.snackbar(
              'Kullanıcı Engellendi',
              'Kullanıcı başarıyla engellendi.',
              backgroundColor: AppColors.primary,
              colorText: AppColors.white,
              snackPosition: SnackPosition.TOP,
            );
          }
        });
      },
    );
  }

  void _handleReportAction() {
    ProfileDialogs.showConfirmationDialog(
      title: 'Kullanıcıyı Bildir',
      message: 'Bu kullanıcıyı bildirmek istediğinizden emin misiniz?',
      confirmText: 'Bildir',
      onConfirm: () async {
        // TODO: Implement report functionality when API is available
        Get.snackbar(
          'Bildirim Alındı',
          'Bildiriminiz alındı. Teşekkür ederiz.',
          backgroundColor: AppColors.info,
          colorText: AppColors.white,
          snackPosition: SnackPosition.TOP,
        );
      },
    );
  }

  void _handleHidePostsAction() {
    ProfileDialogs.showConfirmationDialog(
      title: 'Gönderileri Gizle',
      message: 'Bu kullanıcının gönderilerini gizlemek istediğinizden emin misiniz? Bu işlem geri alınamaz.',
      confirmText: 'Gönderileri Gizle',
      onConfirm: () async {
        // TODO: Implement hide posts functionality when API is available
        Get.snackbar(
          'Gönderiler Gizlendi',
          'Bu kullanıcının gönderileri artık feedinizde görünmeyecek.',
          backgroundColor: AppColors.primary,
          colorText: AppColors.white,
          snackPosition: SnackPosition.TOP,
        );
      },
    );
  }
}
