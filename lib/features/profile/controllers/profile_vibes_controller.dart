import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/profile/controllers/profile_shared_state.dart';

class ProfileVibesController extends BaseController<ProfileSharedState> {
  final _vibeFoldersReturn = Rxn<GetVibeFoldersByUserIdReturn>();

  ProfileVibesController(AuthService authService, ProfileSharedState state) : super(authService, state);

  GetVibeFoldersByUserIdReturn? get vibeFoldersReturn => _vibeFoldersReturn.value;

  @override
  void onInitAsync() async {
    super.onInitAsync();
    await _loadFolders();
  }

  Future<void> _loadFolders() async {
    await runSafe(tag: 'loadFolders', () async {
      final result = await usersApi.getVibeFoldersByUserId(state.userId);
      _vibeFoldersReturn.value = result;
    });
  }
}
