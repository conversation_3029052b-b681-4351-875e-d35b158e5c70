import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/controllers/base_controller.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/core/widgets/ia_search_results_builder.dart';
import 'package:ivent_app/features/profile/controllers/profile_shared_state.dart';

class ProfileFavoritesController extends BaseControllerWithSearch<ProfileSharedState> {
  final _favoritesResult = Rxn<GetFavoritesByUserIdReturn>();
  final _favoritedIventIds = RxList<String>([]);

  ProfileFavoritesController(AuthService authService, ProfileSharedState state) : super(authService, state);

  GetFavoritesByUserIdReturn? get favoritesResult => _favoritesResult.value;
  List<String> get favoritedIventIds => _favoritedIventIds;

  @override
  bool get isResultsEmpty => favoritesResult?.ivents.isEmpty ?? true;

  @override
  InitialSearchBehavior get initialSearchBehavior => InitialSearchBehavior.loaded;

  @override
  Future<void> onSearch([String? query]) async {
    _favoritesResult.value = await usersApi.getFavoritesByUserId(
      state.userId,
      q: query,
    );
    if (favoritesResult != null) {
      _favoritedIventIds.value = favoritesResult!.ivents.map((e) => e.iventId).toList();
    }
  }

  Future<void> toggleFavorite(String iventId) async {
    await runSafe(() async {
      if (favoritedIventIds.contains(iventId)) {
        favoritedIventIds.remove(iventId);
        await iventsApi.unfavoriteIventByIventId(iventId);
      } else {
        favoritedIventIds.add(iventId);
        await iventsApi.favoriteIventByIventId(iventId);
      }
    });
  }
}
