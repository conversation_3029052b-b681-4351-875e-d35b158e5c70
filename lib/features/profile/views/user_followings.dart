import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/widgets/ia_search_results_builder.dart';
import 'package:ivent_app/core/widgets/index.dart';
import 'package:ivent_app/features/profile/controllers/profile_followings_controller.dart';
import 'package:ivent_app/features/profile/profile_pages.dart';

class UserFollowings extends GetView<ProfileFollowingsController> {
  final String userId;

  const UserFollowings({Key? key, required this.userId}) : super(key: key);

  @override
  String? get tag => userId;

  @override
  Widget build(BuildContext context) {
    return IaScaffold.search(
      title: 'Taki<PERSON> Ettiklerin',
      textEditingController: controller.textController,
      body: Obx(() {
        final followingsResult = controller.followingsResult;
        return IaSearchResultsBuilder(
          entityName: 'Kullanıcı',
          isSearching: controller.isSearching,
          isResultsEmpty: controller.isResultsEmpty,
          isQueryEmpty: controller.isQueryEmpty,
          initialSearchBehavior: InitialSearchBehavior.loaded,
          builder: (context) => ListView.separated(
            padding: const EdgeInsets.only(bottom: 100),
            itemCount: followingsResult!.followingCount,
            itemBuilder: (context, index) {
              final following = followingsResult.followings[index];
              return _SearchResult(following: following);
            },
            separatorBuilder: IaListTile.separatorBuilder20,
          ),
        );
      }),
    );
  }
}

class _SearchResult extends StatelessWidget {
  const _SearchResult({
    required this.following,
  });

  final UserListItemWithRelationshipStatus following;

  @override
  Widget build(BuildContext context) {
    return IaListTile.withImageUrl(
      avatarUrl: following.avatarUrl,
      title: '@${following.username}',
      subtitle: following.university,
      onTap: () => Get.toNamed(ProfilePages.userProfile, arguments: following.userId),
      trailing: SharedButtons.moreVertical(),
    );
  }
}
