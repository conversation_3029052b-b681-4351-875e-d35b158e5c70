import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/widgets/layout/panels/ia_bottom_panel.dart';
import 'package:ivent_app/core/widgets/layout/panels/ia_sliding_panel.dart';
import 'package:ivent_app/features/home/<USER>/home_constants.dart';
import 'package:ivent_app/features/home/<USER>/home_panels_controller.dart';
import 'package:ivent_app/features/home/<USER>/feed_page.dart';
import 'package:ivent_app/features/home/<USER>/filter_page.dart';
import 'package:ivent_app/features/home/<USER>/map_page.dart';
import 'package:ivent_app/features/home/<USER>/search_page.dart';
import 'package:sliding_up_panel/sliding_up_panel.dart';

class HomePage extends GetView<HomePanelsController> {
  const HomePage({super.key});

  static const List<Widget> _screens = [
    FeedPage(),
    SearchPage(),
    FilterPage(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(() {
        return PopScope(
          canPop: controller.screenIndex == 0,
          onPopInvokedWithResult: (didPop, result) {
            if (controller.screenIndex != 0) {
              controller.goToFeedPage();
            }
          },
          child: IaSlidingPanel(
            defaultPanelState: PanelState.CLOSED,
            panelController: controller.panelController,
            isDraggable: controller.isPanelDraggable,
            maxHeight: Get.height - MediaQuery.of(context).padding.top - 70,
            minHeight: HomeConstants.panelMinHeight,
            onPanelOpened: controller.setPanelOpen,
            onPanelClosed: controller.setPanelClosed,
            panel: IaBottomPanel(
              showSlideIndicator: controller.isPanelDraggable,
              body: _screens[controller.screenIndex],
            ),
            body: const MapPage(),
          ),
        );
      }),
    );
  }
}
