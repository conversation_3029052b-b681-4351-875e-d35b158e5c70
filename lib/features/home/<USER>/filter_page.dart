import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/widgets/index.dart';
import 'package:ivent_app/features/home/<USER>/home_filter_controller.dart';
import 'package:ivent_app/features/home/<USER>/filter_page/feed_filter_header.dart';
import 'package:ivent_app/features/home/<USER>/filter_page/feed_filter_hobby_catalogue.dart';
import 'package:ivent_app/features/home/<USER>/filter_page/feed_filter_location_options.dart';

class FilterPage extends GetView<HomeFilterController> {
  const FilterPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) => Get.back(),
      child: Obx(() {
        final appliedFilterCount = controller.state.appliedFilterCount;
        return Stack(
          children: [
            const Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: AppDimensions.padding12),
                FeedFilterHeader(),
                SizedBox(height: AppDimensions.padding24),
                FeedFilterLocationOptions(),
                SizedBox(height: AppDimensions.padding20),
                Expanded(child: FeedFilterHobbyCatalogue())
              ],
            ),
            if (appliedFilterCount != 0)
              Column(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  IaFloatingActionButton(
                    text: 'Filtreleri Uygula',
                    onPressed: controller.applyFilters,
                    isEnabled: true,
                  ),
                  const SizedBox(height: AppDimensions.bottomNavigationBarHeight),
                ],
              ),
          ],
        );
      }),
    );
  }
}
