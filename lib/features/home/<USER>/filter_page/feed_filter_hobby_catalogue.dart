import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/widgets/composite/tiles/ia_list_tile.dart';
import 'package:ivent_app/features/home/<USER>/home_filter_controller.dart';
import 'package:ivent_app/features/home/<USER>/filter_page/buttons/feed_filter_special_categories_button.dart';
import 'package:ivent_app/features/home/<USER>/filter_page/hobby_filter_category_box.dart';
import 'package:ivent_app/shared/domain/entities/hobby.dart';

class FeedFilterHobbyCatalogue extends GetView<HomeFilterController> {
  const FeedFilterHobbyCatalogue({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final selectedHobbyIds = controller.state.selectedHobbyIds;
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (selectedHobbyIds.isNotEmpty) _buildHobbiesSelected(selectedHobbyIds),
          if (selectedHobbyIds.isNotEmpty) const SizedBox(height: AppDimensions.padding12),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
              child: ListView.separated(
                padding: const EdgeInsets.only(bottom: 100),
                itemCount: Hobby.hobbyListByParentHobbyName.length,
                itemBuilder: (context, index) {
                  final parentHobbyEntry = Hobby.hobbyListByParentHobbyName.entries.elementAt(index);
                  return HobbyFilterCategoryBox(
                    mainCategory: parentHobbyEntry.key,
                    hobbyList: parentHobbyEntry.value,
                    selectedHobbyIds: controller.state.selectedHobbyIds,
                    onHobbyToggle: controller.toggleHobbySelection,
                  );
                },
                separatorBuilder: IaListTile.separatorBuilder20,
              ),
            ),
          ),
        ],
      );
    });
  }

  Widget _buildHobbiesSelected(List<String> selectedHobbyIds) {
    return Container(
      margin: const EdgeInsets.only(bottom: 10),
      height: AppDimensions.buttonHeightFilterSelectedHobbyTag,
      child: ListView.separated(
        clipBehavior: Clip.none,
        padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
        scrollDirection: Axis.horizontal,
        shrinkWrap: true,
        itemCount: selectedHobbyIds.length,
        itemBuilder: (context, index) {
          final hobbyId = selectedHobbyIds[index];
          return FeedFilterSpecialCategoriesButton(
            onTap: () => controller.toggleHobbySelection(hobbyId),
            isSelected: true,
            text: Hobby.getHobbyNameFromHobbyId(hobbyId),
          );
        },
        separatorBuilder: (context, index) => const SizedBox(width: AppDimensions.padding8),
      ),
    );
  }
}
