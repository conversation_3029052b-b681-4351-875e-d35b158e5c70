import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/widgets/index.dart';
import 'package:ivent_app/features/auth/constants/validation_constants.dart';
import 'package:ivent_app/features/home/<USER>/filter_page/buttons/feed_filter_expand_category_button.dart';
import 'package:ivent_app/shared/domain/entities/hobby.dart';
import 'package:ivent_app/shared/widgets/buttons/hobby_catalogue_button.dart';

class HobbyFilterCategoryBox extends StatefulWidget {
  final void Function(String hobbyId) onHobbyToggle;
  final List<String> selectedHobbyIds;
  final String mainCategory;
  final List<Hobby> hobbyList;

  const HobbyFilterCategoryBox({
    super.key,
    required this.onHobbyToggle,
    required this.selectedHobbyIds,
    required this.mainCategory,
    required this.hobbyList,
  });

  @override
  State<HobbyFilterCategoryBox> createState() => _HobbyFilterCategoryBoxState();
}

class _HobbyFilterCategoryBoxState extends State<HobbyFilterCategoryBox> {
  bool _isExpanded = false;

  bool get _isEverythingSelected => widget.hobbyList.every((hobby) => widget.selectedHobbyIds.contains(hobby.hobbyId));

  void _toggleExpandedCategory() => setState(() => _isExpanded = !_isExpanded);

  @override
  Widget build(BuildContext context) {
    return IaRoundedContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildCategoryTitle(),
          if (_isExpanded) const SizedBox(height: AppDimensions.padding12),
          if (_isExpanded) _buildHobbyTags(),
        ],
      ),
    );
  }

  Widget _buildCategoryTitle() {
    return IaListTile.withSvgIcon(
      iconPath: AppAssets.book,
      title: widget.mainCategory,
      trailing: Row(
        children: [
          IaCheckbox(
            isSelected: _isEverythingSelected,
            onTap: () {
              if (_isEverythingSelected) {
                widget.hobbyList.forEach((hobby) => widget.onHobbyToggle(hobby.hobbyId));
              } else {
                widget.hobbyList.forEach((hobby) {
                  if (!widget.selectedHobbyIds.contains(hobby.hobbyId)) {
                    widget.onHobbyToggle(hobby.hobbyId);
                  }
                });
              }
            },
          ),
          const SizedBox(width: AppDimensions.padding12),
          FeedFilterExpandCategoryButton(onTap: _toggleExpandedCategory, isExpanded: _isExpanded),
        ],
      ),
    );
  }

  Widget _buildHobbyTags() {
    const int maxInitialDisplay = AuthValidationConstants.maxInitialHobbiesDisplayed;
    final int displayCount = _isExpanded ? widget.hobbyList.length : maxInitialDisplay;

    return Wrap(
      crossAxisAlignment: WrapCrossAlignment.start,
      spacing: AppDimensions.padding8,
      runSpacing: AppDimensions.padding8,
      children: widget.hobbyList
          .take(displayCount)
          .map((hobby) => HobbyCatalogueButton(
                onTap: () => widget.onHobbyToggle(hobby.hobbyId),
                text: hobby.hobbyName,
                isSelected: widget.selectedHobbyIds.contains(hobby.hobbyId),
              ))
          .toList(),
    );
  }
}
