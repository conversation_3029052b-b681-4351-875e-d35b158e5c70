import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_rounded_button.dart';
import 'package:ivent_app/features/home/<USER>/home_strings.dart';

class FeedFilterLocationButton extends StatelessWidget {
  final VoidCallback onTap;
  final String text;
  final EdgeInsetsGeometry? margin;

  const FeedFilterLocationButton({
    super.key,
    required this.onTap,
    required this.text,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    return IaRoundedButton(
      margin: margin,
      height: AppDimensions.buttonHeightLocationFilter,
      roundness: AppDimensions.radiusXXL,
      onTap: onTap,
      color: AppColors.white,
      text: '${HomeStrings.seciliKonum} ${text}',
      textStyle: AppTextStyles.size12Regular,
      border: Border.all(color: AppColors.primary, width: 1),
      boxShadow: AppColors.usualShadow,
    );
  }
}
