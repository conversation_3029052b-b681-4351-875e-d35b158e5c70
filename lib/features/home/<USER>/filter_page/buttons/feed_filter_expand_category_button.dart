import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_icon_button.dart';

class FeedFilterExpandCategoryButton extends StatelessWidget {
  final VoidCallback onTap;
  final bool isExpanded;

  const FeedFilterExpandCategoryButton({
    super.key,
    required this.onTap,
    required this.isExpanded,
  });

  @override
  Widget build(BuildContext context) {
    return IaIconButton(
      key: key,
      onPressed: onTap,
      buttonSize: AppDimensions.defaultIconButtonSize,
      iconPath: isExpanded ? AppAssets.chevronUp : AppAssets.chevronDown,
      iconSize: AppDimensions.defaultIconButtonSize,
      iconColor: AppColors.mediumGrey,
    );
  }
}
