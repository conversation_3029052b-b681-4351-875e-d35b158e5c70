import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';

class FeedFilterLocationSlider extends StatelessWidget {
  final double minValue;
  final double maxValue;
  final int divisions;
  final double initialValue;
  final double value;
  final Function(double newValue) onChanged;
  final String label;

  const FeedFilterLocationSlider({
    super.key,
    required this.value,
    this.minValue = 0,
    this.maxValue = 10,
    this.divisions = 10,
    this.initialValue = 0,
    required this.onChanged,
    this.label = '',
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: AppDimensions.sliderHeight,
      child: SliderTheme(
        data: SliderTheme.of(context).copyWith(
          trackHeight: AppDimensions.sliderHeight * 0.25,
          thumbShape: _CustomThumbShape(),
          thumbColor: AppColors.warning,
          overlayColor: Colors.transparent,
          activeTrackColor: AppColors.primary,
          inactiveTickMarkColor: AppColors.transparent,
          activeTickMarkColor: AppColors.primary,
          inactiveTrackColor: AppColors.grey500.withValues(alpha: 0.2),
          valueIndicatorShape: const DropSliderValueIndicatorShape(),
          valueIndicatorColor: AppColors.primary,
          valueIndicatorTextStyle: AppTextStyles.size14Regular.copyWith(color: AppColors.white),
        ),
        child: Slider(
          padding: EdgeInsets.zero,
          autofocus: false,
          value: value,
          min: 0,
          max: 10,
          onChanged: onChanged,
          divisions: 10,
          label: label,
        ),
      ),
    );
  }
}

class _CustomThumbShape extends SliderComponentShape {
  static const double _thumbRadius = 8.0;
  static const double _borderWidth = 2.0;

  @override
  Size getPreferredSize(bool isEnabled, bool isDiscrete) {
    return const Size.fromRadius(_thumbRadius + _borderWidth);
  }

  @override
  void paint(
    PaintingContext context,
    Offset thumbCenter, {
    Animation<double>? activationAnimation,
    Animation<double>? enableAnimation,
    bool? isDiscrete,
    bool? isEnabled,
    bool? isOnTop,
    TextPainter? labelPainter,
    double? textScaleFactor,
    Size? sizeWithOverflow,
    RenderBox? parentBox,
    SliderThemeData? sliderTheme,
    TextDirection? textDirection,
    double? value,
    double? textScaleValue,
  }) {
    final Canvas canvas = context.canvas;

    final Paint borderPaint = Paint()
      ..color = AppColors.primary
      ..style = PaintingStyle.stroke
      ..strokeWidth = _borderWidth;

    final Paint fillPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;

    canvas.drawCircle(thumbCenter, _thumbRadius, borderPaint);
    canvas.drawCircle(thumbCenter, _thumbRadius - _borderWidth, fillPaint);
  }
}
