import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/composite/buttons/home_buttons.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_text_button.dart';
import 'package:ivent_app/features/home/<USER>/home_strings.dart';
import 'package:ivent_app/features/home/<USER>/home_filter_controller.dart';

class FeedFilterHeader extends GetView<HomeFilterController>  {
  const FeedFilterHeader({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IaTextButton(
            onPressed: controller.clearFilters,
            text: HomeStrings.filtreleriTemizle,
            textStyle: AppTextStyles.size16MediumTextSecondary,
          ),
          HomeButtons.closeButtonLight(onTap: () => Get.back()),
        ],
      ),
    );
  }
}
