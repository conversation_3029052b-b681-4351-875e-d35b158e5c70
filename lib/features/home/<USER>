import 'package:get/get.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/home/<USER>/home_accounts_controller.dart';
import 'package:ivent_app/features/home/<USER>/home_feed_controller.dart';
import 'package:ivent_app/features/home/<USER>/home_filter_controller.dart';
import 'package:ivent_app/features/home/<USER>/home_ivents_controller.dart';
import 'package:ivent_app/features/home/<USER>/home_location_controller.dart';
import 'package:ivent_app/features/home/<USER>/home_map_controller.dart';
import 'package:ivent_app/features/home/<USER>/home_panels_controller.dart';
import 'package:ivent_app/features/home/<USER>/home_search_controller.dart';
import 'package:ivent_app/features/home/<USER>/home_state_manager.dart';

class HomeBindings implements Bindings {
  @override
  void dependencies() {
    final service = Get.find<AuthService>();
    final state = Get.put(HomeSharedState(), permanent: true);

    Get.lazyPut(() => HomeAccountsController(service, state), fenix: true);
    Get.lazyPut(() => HomeFeedController(service, state), fenix: true);
    Get.lazyPut(() => HomeFilterController(service, state), fenix: true);
    Get.lazyPut(() => HomeIventsController(service, state), fenix: true);
    Get.lazyPut(() => HomeSearchController(service, state), fenix: true);
    Get.lazyPut(() => HomePanelsController(service, state), fenix: true);
    Get.lazyPut(() => HomeMapController(service, state), fenix: true);
    Get.lazyPut(() => HomeLocationController(service, state), fenix: true);
  }
}
