import 'package:get/get.dart';
import 'package:ivent_app/features/home/<USER>';
import 'package:ivent_app/features/home/<USER>/home_page.dart';
import 'package:ivent_app/features/home/<USER>/location_page.dart';

class HomePages {
  HomePages._();

  static const _prefix = '/home';

  static const homePage = '$_prefix/homePage';
  static const locationPage = '$_prefix/locationPage';

  static final routes = [
    GetPage(
      name: homePage,
      page: () => const HomePage(),
      binding: HomeBindings(),
    ),
    GetPage(
      name: locationPage,
      page: () => const LocationPage(),
    ),
  ];
}
