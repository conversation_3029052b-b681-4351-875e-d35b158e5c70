import 'package:calendar_date_picker2/calendar_date_picker2.dart';
import 'package:flutter/widgets.dart';
import 'package:intl/intl.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/foundation/containers/ia_rounded_container.dart';

class Calendar extends StatefulWidget {
  final Function(List<DateTime?> selectedDates) onDateSelected;
  final Function(List<DateTime?> selectedDates)? onRangeSelected;
  final List<DateTime?>? initiallySelectedDates;
  final bool canSelectRange;

  const Calendar({
    super.key,
    required this.onDateSelected,
    this.onRangeSelected,
    this.initiallySelectedDates,
    this.canSelectRange = true,
  });

  @override
  State<Calendar> createState() => _CalendarState();
}

class _CalendarState extends State<Calendar> {
  List<DateTime?>? _selectedDates;

  List<DateTime?> get selectedDates => _selectedDates ?? widget.initiallySelectedDates ?? [];

  @override
  Widget build(BuildContext context) {
    return IaRoundedContainer(
      padding: const EdgeInsets.all(AppDimensions.padding12),
      width: MediaQuery.of(context).size.width - 40,
      roundness: AppDimensions.radiusXL,
      boxShadow: AppColors.usualShadow,
      color: AppColors.white,
      child: Column(
        children: [
          CalendarDatePicker2(
            value: selectedDates,
            onValueChanged: (dates) {
              setState(() => _selectedDates = dates);
              if (widget.canSelectRange) {
                widget.onRangeSelected?.call(dates);
              }
            },
            config: CalendarDatePicker2WithActionButtonsConfig(
              controlsHeight: 40,
              dayTextStyle: AppTextStyles.size14Bold,
              currentDate: DateTime.now(),
              firstDayOfWeek: 1,
              calendarType: widget.canSelectRange ? CalendarDatePicker2Type.range : CalendarDatePicker2Type.single,
              selectedRangeDayTextStyle: AppTextStyles.size14Bold.copyWith(color: AppColors.white),
              selectedRangeHighlightColor: AppColors.primaryLight,
              selectedDayTextStyle: AppTextStyles.size14Bold.copyWith(color: AppColors.white),
              selectedDayHighlightColor: AppColors.primary,
              centerAlignModePicker: true,
            ),
          ),
          _buildButtonRow(),
        ],
      ),
    );
  }

  Row _buildButtonRow() {
    return Row(
      children: [
        Expanded(child: _buildFirstButton()),
        const SizedBox(width: 20),
        Expanded(child: _buildSecondButton()),
      ],
    );
  }

  Widget _buildFirstButton() {
    var text;
    if (widget.canSelectRange) {
      if (selectedDates.isEmpty) {
        text = 'Başlangıç Tarihi Seçiniz';
      } else if (selectedDates.length == 1) {
        text = 'Bitiş Tarihi Seçiniz';
      } else if (selectedDates.length == 2) {
        final startDate = DateFormat('d MMM').format(selectedDates[0]!);
        final endDate = DateFormat('d MMM').format(selectedDates[1]!);
        text = '$startDate - $endDate';
      }
    } else {
      if (selectedDates.isEmpty) {
        text = 'Tarih Seçiniz';
      } else {
        final date = DateFormat('d MMM').format(selectedDates[0]!);
        text = date;
      }
    }
    return _buildButton(
      child: Text(
        text,
        style: AppTextStyles.size14Medium,
        overflow: TextOverflow.fade,
        maxLines: 1,
        softWrap: false,
      ),
    );
  }

  Widget _buildSecondButton() {
    var isEnabled;
    if (widget.canSelectRange) {
      isEnabled = selectedDates.length == 2;
    } else {
      isEnabled = selectedDates.isNotEmpty;
    }
    return _buildButton(
      onTap: () {
        if (!isEnabled) return;
        widget.onDateSelected(selectedDates);
      },
      color: isEnabled ? AppColors.primary : AppColors.lightGrey,
      child: Text(
        'Bitti',
        style: isEnabled ? AppTextStyles.size14Medium.copyWith(color: AppColors.white) : AppTextStyles.size14Medium,
        overflow: TextOverflow.fade,
        maxLines: 1,
        softWrap: false,
      ),
    );
  }

  GestureDetector _buildButton({
    required Widget child,
    Color color = AppColors.lightGrey,
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: color,
        ),
        padding: const EdgeInsets.symmetric(horizontal: 5),
        height: 33,
        child: Center(child: child),
      ),
    );
  }
}
