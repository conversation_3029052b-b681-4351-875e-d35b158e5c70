import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/widgets/composite/tiles/ia_list_tile.dart';
import 'package:ivent_app/features/home/<USER>/home_location_controller.dart';

class FeedLocationUseMyLocationTile extends GetView<HomeLocationController> {
  const FeedLocationUseMyLocationTile({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final userLocation = controller.mapboxController.userLocation;
      return IaListTile.withSvgIcon(
        iconPath: AppAssets.navigation,
        iconColor: AppColors.white,
        avatarColor: AppColors.primary,
        title: 'Mevcut Konumumu Kullan',
        subtitle: userLocation?.address,
        onTap: controller.useCurrentLocation,
      );
    });
  }
}
