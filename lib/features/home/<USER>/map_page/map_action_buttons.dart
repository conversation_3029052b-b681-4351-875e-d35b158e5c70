import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/enums/user_type_enum.dart';
import 'package:ivent_app/core/widgets/composite/buttons/home_buttons.dart';
import 'package:ivent_app/features/home/<USER>/home_map_controller.dart';

class MapActionButtons extends GetView<HomeMapController> {
  const MapActionButtons({super.key});

  @override
  Widget build(BuildContext context) {
    return Positioned(
      right: AppDimensions.padding20,
      bottom: 170, // TODO: Heights will be adjusted
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          HomeButtons.findUserLocation(
            onTap: controller.mapboxController.moveCameraToUserLocation,
          ),
          if (controller.sessionUser.sessionRole.value == UserTypeEnum.CREATOR)
            Padding(
              padding: const EdgeInsets.only(top: AppDimensions.padding8),
              child: HomeButtons.createIventButtonL(),
            ),
        ],
      ),
    );
  }
}
