import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_rounded_button.dart';
import 'package:ivent_app/core/widgets/foundation/graphics/ia_svg_icon.dart';

class MapDateButton extends StatelessWidget {
  final VoidCallback onTap;
  final String text;

  const MapDateButton({
    super.key,
    required this.onTap,
    required this.text,
  });

  @override
  Widget build(BuildContext context) {
    return IaRoundedButton(
      onTap: onTap,
      roundness: AppDimensions.radiusXL,
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding12),
      height: AppDimensions.buttonHeightMapDateFilter,
      boxShadow: AppColors.usualShadow,
      color: AppColors.white,
      text: text,
      textStyle: AppTextStyles.size12Bold,
      leading: const IaSvgIcon(iconPath: AppAssets.calendar, iconColor: AppColors.black),
    );
  }
}
