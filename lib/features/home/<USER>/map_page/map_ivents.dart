import 'dart:math';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/features/home/<USER>/map_page/map_ivent_box.dart';

class MapIvents extends StatelessWidget {
  final List<IventCardItem> iventBanners;

  const MapIvents({super.key, required this.iventBanners});

  double get _width => min(
        iventBanners.length * 190 * 0.87 +
            (iventBanners.length - 1) * AppDimensions.padding8 +
            2 * AppDimensions.padding20,
        Get.width,
      );

  @override
  Widget build(BuildContext context) {
    if (iventBanners.isEmpty) return const SizedBox.shrink();
    return Positioned(
      bottom: 170,
      left: 0,
      child: Container(
        height: 190, // TODO: Move to constants
        width: _width,
        child: ListView.separated(
          scrollDirection: Axis.horizontal,
          physics: const BouncingScrollPhysics(),
          shrinkWrap: true,
          padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
          itemCount: iventBanners.length,
          itemBuilder: (context, index) => MapIventBox(iventBanner: iventBanners[index]),
          separatorBuilder: (context, index) => const SizedBox(width: AppDimensions.padding8),
        ),
      ),
    );
  }
}
