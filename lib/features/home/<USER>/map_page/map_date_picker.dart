import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/features/home/<USER>/common/calendar.dart';
import 'package:ivent_app/features/home/<USER>/map_page/buttons/map_date_button.dart';

class MapScreenDatePicker extends StatefulWidget {
  final Function(DateTime date) onDateSelected;

  const MapScreenDatePicker({
    super.key,
    required this.onDateSelected,
  });

  @override
  State<MapScreenDatePicker> createState() => _MapScreenDatePickerState();
}

class _MapScreenDatePickerState extends State<MapScreenDatePicker> {
  bool _isCalendarVisible = false;
  DateTime _selectedDate = DateTime.now();

  void _toggleCalendarVisibility() => setState(() => _isCalendarVisible = !_isCalendarVisible);

  void _applySelectedDates(List<DateTime?> dates) {
    _selectedDate = dates[0]!;
    widget.onDateSelected(_selectedDate);
    _toggleCalendarVisibility();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        MapDateButton(
          onTap: _toggleCalendarVisibility,
          text: DateFormat('d MMMM EEEE').format(_selectedDate),
        ),
        const SizedBox(height: AppDimensions.padding20),
        if (_isCalendarVisible)
          Calendar(
            initiallySelectedDates: [_selectedDate],
            onDateSelected: _applySelectedDates,
            canSelectRange: false,
          ),
      ],
    );
  }
}
