import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/enums/user_type_enum.dart';
import 'package:ivent_app/core/widgets/composite/buttons/home_buttons.dart';
import 'package:ivent_app/features/home/<USER>/home_feed_controller.dart';
import 'package:ivent_app/features/home/<USER>/feed_page/feed.dart';
import 'package:ivent_app/features/home/<USER>/feed_page/feed_header.dart';
import 'package:ivent_app/features/home/<USER>/feed_page/feed_date_picker.dart';

class FeedPage extends GetView<HomeFeedController> {
  const FeedPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Column(
          children: [
            const SizedBox(height: AppDimensions.padding8),
            const FeedHeader(),
            FeedDatePicker(
              onDateRangeSelected: controller.setSelectedDates,
              onDateButtonPressed: controller.toggleTimeFilter,
            ),
            const Expanded(child: Feed()),
          ],
        ),
        if (controller.sessionUser.sessionRole == UserTypeEnum.CREATOR)
          Positioned(
            right: AppDimensions.padding20,
            bottom: AppDimensions.buttonSizeCreateIventL + AppDimensions.padding20,
            child: HomeButtons.createIventButtonL(),
          ),
      ],
    );
  }
}
