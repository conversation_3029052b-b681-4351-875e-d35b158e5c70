import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/features/home/<USER>/feed_page/buttons/feed_date_button.dart';

class FeedDateScroll extends StatelessWidget {
  final int? selectedIndex;
  final Function(int index) onDateButtonPressed;

  const FeedDateScroll({
    required this.selectedIndex,
    required this.onDateButtonPressed,
  });

  static const dateOptions = [
    'Zaman Aralığı Seç',
    '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON><PERSON>',
    '<PERSON>u Hafta',
    '<PERSON><PERSON><PERSON>',
    '<PERSON>u <PERSON><PERSON>',
    '<PERSON>u <PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
  ];

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: AppDimensions.buttonHeightDateFilter,
      child: ListView.separated(
        clipBehavior: Clip.none,
        padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
        itemCount: dateOptions.length,
        scrollDirection: Axis.horizontal,
        itemBuilder: (BuildContext context, index) {
          return FeedDateButton(
            onTap: () => onDateButtonPressed(index),
            isSelected: selectedIndex == index,
            text: dateOptions[index],
            iconPath: index == 0 ? AppAssets.calendar : null,
          );
        },
        separatorBuilder: (BuildContext context, int index) => const SizedBox(width: AppDimensions.padding8),
      ),
    );
  }
}
