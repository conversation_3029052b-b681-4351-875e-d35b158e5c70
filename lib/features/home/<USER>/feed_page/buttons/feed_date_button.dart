import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_rounded_button.dart';
import 'package:ivent_app/core/widgets/foundation/graphics/ia_svg_icon.dart';

class FeedDateButton extends StatelessWidget {
  final VoidCallback onTap;
  final bool isSelected;
  final String text;
  final String? iconPath;
  final EdgeInsetsGeometry? margin;

  const FeedDateButton({
    super.key,
    required this.onTap,
    required this.isSelected,
    required this.text,
    this.iconPath,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    return IaRoundedButton(
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding12),
      margin: margin,
      height: AppDimensions.buttonHeightDateFilter,
      roundness: AppDimensions.buttonRadiusL,
      onTap: onTap,
      color: isSelected ? AppColors.primary : AppColors.white,
      text: text,
      textStyle: isSelected ? AppTextStyles.size12RegularWhite : AppTextStyles.size12Regular,
      leading: iconPath != null
          ? IaSvgIcon(
              iconPath: iconPath!,
              iconSize: AppDimensions.buttonSizeDateFilterCalender,
              iconColor: isSelected ? AppColors.white : AppColors.black,
            )
          : null,
      boxShadow: AppColors.usualShadow,
    );
  }
}
