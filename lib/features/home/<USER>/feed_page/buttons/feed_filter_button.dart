import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_icon_button.dart';
import 'package:ivent_app/core/widgets/foundation/containers/ia_rounded_container.dart';

class FeedFilterButton extends StatelessWidget {
  final VoidCallback? onTap;
  final bool isActive;
  final int filterCount;

  const FeedFilterButton({
    super.key,
    this.onTap,
    required this.isActive,
    required this.filterCount,
  });

  @override
  Widget build(BuildContext context) {
    return IaIconButton(
      onPressed: onTap,
      buttonSize: AppDimensions.defaultIconButtonSize,
      iconPath: AppAssets.filter,
      iconSize: AppDimensions.defaultIconButtonSize,
      iconColor: isActive ? AppColors.primary : AppColors.darkGrey,
      overlay: filterCount != 0
          ? Positioned(
              bottom: -2,
              right: 1,
              child: IaRoundedContainer(
                text: filterCount.toString(),
                textStyle: AppTextStyles.size12BoldPrimary,
              ),
            )
          : null,
    );
  }
}
