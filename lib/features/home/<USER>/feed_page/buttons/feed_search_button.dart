import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_icon_button.dart';

class FeedSearchButton extends StatelessWidget {
  final VoidCallback onTap;

  const FeedSearchButton({super.key, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return IaIconButton(
      onPressed: onTap,
      buttonSize: AppDimensions.defaultIconButtonSize,
      iconPath: AppAssets.searchMagnifyingGlass,
      iconSize: AppDimensions.defaultIconButtonSize,
      iconColor: AppColors.darkGrey,
    );
  }
}
