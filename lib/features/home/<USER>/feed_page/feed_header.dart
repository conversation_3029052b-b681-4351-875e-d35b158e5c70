import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/features/home/<USER>/home_panels_controller.dart';
import 'package:ivent_app/features/home/<USER>/feed_page/buttons/feed_filter_button.dart';
import 'package:ivent_app/features/home/<USER>/feed_page/buttons/feed_search_button.dart';

class FeedHeader extends GetView<HomePanelsController> {
  const FeedHeader({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
      child: Row(
        children: [
          const _IventKesfet(),
          const Spacer(),
          FeedSearchButton(onTap: controller.goToSearchPage),
          const SizedBox(width: 20),
          Obx(() => FeedFilterButton(
                isActive: controller.state.appliedFilterCount != 0,
                filterCount: controller.state.appliedFilterCount,
                onTap: controller.goToFilterPage,
              )),
        ],
      ),
    );
  }
}

class _IventKesfet extends StatelessWidget {
  const _IventKesfet();

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Text('iVent', style: AppTextStyles.size20Bold),
        Text('Keşfet', style: AppTextStyles.size20BoldPrimary),
      ],
    );
  }
}
