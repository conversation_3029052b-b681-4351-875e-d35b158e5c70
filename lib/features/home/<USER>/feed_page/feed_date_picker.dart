import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/features/home/<USER>/common/calendar.dart';
import 'package:ivent_app/features/home/<USER>/feed_page/feed_date_scroll.dart';

class FeedDatePicker extends StatefulWidget {
  final Function(DateTime startDate, DateTime endDate) onDateRangeSelected;
  final Function(int index) onDateButtonPressed;

  const FeedDatePicker({
    super.key,
    required this.onDateRangeSelected,
    required this.onDateButtonPressed,
  });

  @override
  State<FeedDatePicker> createState() => _FeedDatePickerState();
}

class _FeedDatePickerState extends State<FeedDatePicker> {
  bool _isCalendarVisible = false;
  int? _selectedIndex = null;
  List<DateTime?> _selectedDates = [];

  void _toggleCalendarVisibility() {
    setState(() => _isCalendarVisible = !_isCalendarVisible);
    print('Calendar visibility toggled: $_selectedDates');
  }

  void _onDateButtonPressed(int index) => setState(() {
        _selectedIndex = index;

        if (index == 0) {
          print('Calendar button pressed');
          _toggleCalendarVisibility();
        } else {
          print('Other button pressed');
          if (_isCalendarVisible) {
            _toggleCalendarVisibility();
          }
          if (index != 1) {
            _selectedDates = [];
          }
          widget.onDateButtonPressed(index);
        }
      });

  void _applySelectedDates(List<DateTime?> dates) {
    _selectedDates = dates;
    widget.onDateRangeSelected(dates[0]!, dates[1]!);
    _toggleCalendarVisibility();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: AppDimensions.padding20),
      child: Column(
        children: [
          FeedDateScroll(
            selectedIndex: _selectedIndex,
            onDateButtonPressed: _onDateButtonPressed,
          ),
          if (_isCalendarVisible) const SizedBox(height: AppDimensions.padding20),
          if (_isCalendarVisible)
            Calendar(
              initiallySelectedDates: _selectedDates,
              onDateSelected: _applySelectedDates,
              onRangeSelected: (dates) => setState(() => _selectedDates = dates),
            ),
        ],
      ),
    );
  }
}
