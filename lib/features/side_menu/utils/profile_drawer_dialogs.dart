import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/core/widgets/index.dart';

class ProfileDrawerDialogs {

  static void showDeleteAccountDialog(BuildContext context) {
    Get.dialog(
      AlertDialog(
        backgroundColor: AppColors.background,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusL),
        ),
        insetPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
        title: Row(
          children: [
            const IaSvgIcon(
              iconPath: AppAssets.circleWarning,
              iconSize: 24,
              iconColor: AppColors.error,
            ),
            const SizedBox(width: AppDimensions.padding12),
            Text(
              'Hesabı Sil',
              style: AppTextStyles.size16Bold.copyWith(color: AppColors.error),
            ),
          ],
        ),
        content: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 300),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Hesabınızı silmek istediğinizden emin misiniz?',
                style: AppTextStyles.size14Bold,
              ),
              const SizedBox(height: AppDimensions.padding8),
              Text(
                'Aşağıdaki verileriniz kalıcı olarak silinecektir:',
                style: AppTextStyles.size14Regular.copyWith(color: AppColors.textSecondary),
              ),
              const SizedBox(height: AppDimensions.padding12),
              _buildDeleteWarningItem('• Profil bilgileriniz'),
              _buildDeleteWarningItem('• Oluşturduğunuz iVent\'ler'),
              _buildDeleteWarningItem('• Arkadaşlık bağlantılarınız'),
              _buildDeleteWarningItem('• Memories\'leriniz'),
              _buildDeleteWarningItem('• Favorileriniz'),
              const SizedBox(height: AppDimensions.padding16),
              Container(
                padding: const EdgeInsets.all(AppDimensions.padding12),
                decoration: BoxDecoration(
                  color: AppColors.error.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  border: Border.all(color: AppColors.error.withOpacity(0.3)),
                ),
                child: Row(
                  children: [
                    const IaSvgIcon(
                      iconPath: AppAssets.circleWarning,
                      iconSize: 20,
                      iconColor: AppColors.error,
                    ),
                    const SizedBox(width: AppDimensions.padding8),
                    Expanded(
                      child: Text(
                        'Bu işlem geri alınamaz!',
                        style: AppTextStyles.size12Bold.copyWith(color: AppColors.error),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text(
              'İptal',
              style: AppTextStyles.size14Medium.copyWith(color: AppColors.textSecondary),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              Get.find<AuthService>().deleteAccount();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: AppColors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              ),
            ),
            child: Text(
              'Hesabı Sil',
              style: AppTextStyles.size14Bold.copyWith(color: AppColors.white),
            ),
          ),
        ],
      ),
    );
  }

  static Widget _buildDeleteWarningItem(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppDimensions.padding4),
      child: Text(
        text,
        style: AppTextStyles.size12Regular.copyWith(color: AppColors.textSecondary),
      ),
    );
  }
}
