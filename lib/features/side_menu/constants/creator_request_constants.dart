class CreatorRequestConstants {
  static const String pageTitle = 'iVent Creator Başvurusu';
  static const String headerTitle = 'iVent Creator Ol';
  static const String headerSubtitle = 'Etkinlik dünyasında öne çık!';
  static const String headerDescription = 
      'iVent Creator programına katılarak etkinlik organizasyonu konusundaki uzmanlığınızı sergileyebilir, daha geniş kitlelere ulaşabilir ve özel avantajlardan yararlanabilirsiniz.';
  
  static const String benefitsTitle = 'Creator Avantajları';
  static const String benefitsDetailsButton = 'Detayları Gör';
  static const String benefitsMoreText = 've %d avantaj daha...';
  
  static const String requirementsTitle = 'Başvuru Şartları (Debug Mode)';
  static const String requirementsDetailsButton = 'Detayları Gör';
  
  static const String applicationTitle = 'Başvuru';
  static const String applicationProcessTitle = 'Başvuru Süreci';
  static const String applicationProcessSteps = 
      '1. Başvuru formunu gönderin\n'
      '2. Başvurunuz incelenir (1-3 iş günü)\n'
      '3. Sonuç bildirimini alın\n'
      '4. Onay durumunda Creator hesabınız aktifleşir';
  
  static const String buttonSubmitted = 'Başvuru Gönderildi';
  static const String buttonContinue = 'Devam Et';
  static const String buttonCannotApply = 'Başvuru Yapılamaz';
  
  static const String cannotApplyMessage = 
      'Zaten bir iVent Creator\'sınız veya başvurunuz değerlendiriliyor.';
  
  static const String authErrorTitle = 'Hata';
  static const String authErrorMessage = 'Oturum açmanız gerekiyor.';
  
  static const double iconSize = 28.0;
  static const double statusIconSize = 16.0;
  static const double benefitIconSize = 16.0;
  static const double requirementIconSize = 16.0;
  static const double loadingIndicatorSize = 20.0;
  static const double loadingStrokeWidth = 2.0;
  
  static const double iconPadding = 12.0;
  static const double headerSpacing = 4.0;
  static const double benefitItemSpacing = 8.0;
  static const double requirementItemSpacing = 8.0;
  static const double moreTextTopPadding = 8.0;
  static const double cannotApplyTopPadding = 8.0;
  
  static const double headerOpacity = 0.2;
  static const double descriptionOpacity = 0.9;
  static const double statusBackgroundOpacity = 0.1;
  static const double statusBorderWidth = 1.0;
}
