import 'package:flutter/material.dart';

class DrawerConstants {
  static const Color creatorButtonColor = Color(0xFFFF8C00);
  
  static const String eduVerificationTitle = 'edu.tr Onayla';
  static const String eduVerificationSubtitle = 'Üniversite Etkinliklerini Keşfetmek İçin';
  
  static const String creatorRequestTitle = 'iVent Creator Olmak';
  static const String creatorRequestSubtitle = 'Eşsiz Deneyimler ve Çok Daha Fazlası...';
  
  static const String accountManagementText = 'Hesap Yönetimi';
  static const String myPagesText = 'Sayfalarım';
  static const String createPageText = 'Sayfa Oluştur';
  static const String createPageSubtext = 'Mekan veya Topluluklar İçin';
  static const String pagesLoadingText = 'Sayfalar yükleniyor...';

  // #region Temporarily added items
  static const String supportText = 'Destek';
  static const String privacyText = 'Gizlilik Policy';
  static const String termsOfServiceText = 'Hizmet Şartları';
  static const String blockedUsersText = 'Engellenen Kullanıcılar';
  // #endregion
  
  static const String deleteAccountText = 'Hesabı Sil';
  static const String logoutText = 'Çıkış Yap';
  
  static const Map<String, String> membershipStatusMap = {
    'admin': 'Yardımcı Admin',
    'creator': 'Sayfa Admini',
    'moderator': 'Sayfa Admini',
  };
  
  static String getMembershipStatusText(String status) {
    return membershipStatusMap[status] ?? 'Üye';
  }
}
