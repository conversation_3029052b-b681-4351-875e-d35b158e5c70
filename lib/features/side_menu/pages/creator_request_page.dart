import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/side_menu/constants/creator_request_constants.dart';
import 'package:ivent_app/features/profile/controllers/profile_shared_state.dart';
import 'package:ivent_app/features/side_menu/widgets/creator_request/creator_request_application_section.dart';
import 'package:ivent_app/features/side_menu/widgets/creator_request/creator_request_benefits_section.dart';
import 'package:ivent_app/features/side_menu/widgets/creator_request/creator_request_header_section.dart';
import 'package:ivent_app/features/side_menu/widgets/creator_request/creator_request_requirements_section.dart';
import 'package:ivent_app/features/side_menu/widgets/creator_request/creator_request_status_section.dart';
import 'package:ivent_app/features/side_menu/controllers/creator_request_controller.dart';

class CreatorRequestPage extends StatefulWidget {
  const CreatorRequestPage({Key? key}) : super(key: key);

  @override
  State<CreatorRequestPage> createState() => _CreatorRequestPageState();
}

class _CreatorRequestPageState extends State<CreatorRequestPage> {
  CreatorRequestController? controller;

  @override
  void initState() {
    super.initState();
    // Create controller manually
    try {
      final authService = Get.find<AuthService>();
      controller = CreatorRequestController(authService, Get.find<ProfileSharedState>());
      Get.put(controller!);
    } catch (e) {
      // If AuthService is not found, navigate back
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Get.back();
        Get.snackbar(
          CreatorRequestConstants.authErrorTitle,
          CreatorRequestConstants.authErrorMessage,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      });
    }
  }

  @override
  void dispose() {
    if (controller != null) {
      Get.delete<CreatorRequestController>();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (controller == null) {
      return IaScaffold.noSearch(
        title: CreatorRequestConstants.pageTitle,
        body: const Center(
          child: CircularProgressIndicator(),
        ),
        showBackButton: true,
      );
    }

    return IaScaffold.noSearch(
      title: CreatorRequestConstants.pageTitle,
      body: Obx(() {
        if (controller!.isLoading()) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(AppDimensions.padding16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const CreatorRequestHeaderSection(),
              const SizedBox(height: AppDimensions.padding24),
              CreatorRequestStatusSection(
                isApplicationSubmitted: controller!.isApplicationSubmitted,
                canApply: controller!.canApply,
                applicationStatus: controller!.applicationStatus,
              ),
              const SizedBox(height: AppDimensions.padding24),
              CreatorRequestBenefitsSection(
                creatorBenefits: controller!.creatorBenefits,
                onShowBenefitsDialog: controller!.showBenefitsDialog,
              ),
              const SizedBox(height: AppDimensions.padding24),
              if (kDebugMode) ...[
                CreatorRequestRequirementsSection(
                  requirementDescriptions: controller!.requirementDescriptions,
                  levelRequirements: controller!.levelRequirements,
                  onShowRequirementsDialog: controller!.showRequirementsDialog,
                ),
                const SizedBox(height: AppDimensions.padding24),
              ],
              CreatorRequestApplicationSection(
                canApply: controller!.canApply,
                isApplicationSubmitted: controller!.isApplicationSubmitted,
                isLoading: controller!.isLoading(),
              ),
              const SizedBox(height: AppDimensions.padding32),
            ],
          ),
        );
      }),
      showBackButton: true,
    );
  }
}
