import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/side_menu/constants/profile_constants.dart';
import 'package:ivent_app/features/side_menu/utils/edu_verification_controller_helper.dart';
import 'package:ivent_app/features/side_menu/widgets/edu_verification/email_step.dart';
import 'package:ivent_app/features/side_menu/widgets/edu_verification/graduation_status_selection.dart';
import 'package:ivent_app/features/side_menu/widgets/edu_verification/university_search_widget.dart';
import 'package:ivent_app/features/side_menu/widgets/edu_verification/verification_action_buttons.dart';
import 'package:ivent_app/features/side_menu/widgets/edu_verification/verification_code_step.dart';
import 'package:ivent_app/features/side_menu/widgets/edu_verification/verification_progress_indicator.dart';
import 'package:ivent_app/features/side_menu/controllers/edu_verification_controller.dart';

class EduVerificationPage extends StatefulWidget {
  const EduVerificationPage({Key? key}) : super(key: key);

  @override
  State<EduVerificationPage> createState() => _EduVerificationPageState();
}

class _EduVerificationPageState extends State<EduVerificationPage> {
  EduVerificationController? controller;

  @override
  void initState() {
    super.initState();
    controller = EduVerificationControllerHelper.initializeController();
  }

  @override
  void dispose() {
    if (controller != null) {
      EduVerificationControllerHelper.disposeController();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (controller == null) {
      return IaScaffold.noSearch(
        title: ProfileConstants.eduVerificationTitle,
        body: const Center(child: CircularProgressIndicator()),
        showBackButton: true,
      );
    }

    return IaScaffold.noSearch(
      title: ProfileConstants.eduVerificationTitle,
      body: Obx(() {
        if (controller!.isLoading()) {
          return const Center(child: CircularProgressIndicator());
        }
        return _buildBody();
      }),
      showBackButton: true,
    );
  }

  Widget _buildBody() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppDimensions.padding16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Obx(() => VerificationProgressIndicator(
                currentStep: controller!.currentStep,
                totalSteps: 3,
                stepTitle: controller!.stepTitle,
                stepDescription: controller!.stepDescription,
              )),
          const SizedBox(height: AppDimensions.padding24),
          Obx(() {
            switch (controller!.currentStep) {
              case 1:
                return EmailStep(
                  emailController: controller!.emailController,
                  isEmailValid: controller!.isEmailValid,
                  onChanged: controller!.triggerUpdate,
                );
              case 2:
                return VerificationCodeStep(
                  email: controller!.emailController.text,
                  verificationCodeController: controller!.verificationCodeController,
                  isCodeValid: controller!.isCodeValid,
                  resendTimer: controller!.resendTimer,
                  onChanged: controller!.triggerUpdate,
                  onResendCode: controller!.resendVerificationCode,
                );
              case 3:
                return Column(
                  children: [
                    UniversitySearchWidget(
                      universitySearchController: controller!.universitySearchController,
                      universities: controller!.universities,
                      selectedUniversity: controller!.selectedUniversity,
                      onSearchChanged: controller!.searchUniversities,
                      onUniversitySelected: controller!.selectUniversity,
                      onSelectedUniversityRemoved: controller!.clearSelectedUniversity,
                    ),
                    const SizedBox(height: AppDimensions.padding16),
                    GraduationStatusSelection(
                      selectedGradStatus: controller!.selectedGradStatus,
                      onStatusSelected: controller!.selectGradStatus,
                    ),
                  ],
                );
              default:
                return const SizedBox.shrink();
            }
          }),
          const SizedBox(height: AppDimensions.padding32),
          Obx(() => VerificationActionButtons(
                currentStep: controller!.currentStep,
                isLoading: controller!.isLoading(),
                isMainButtonEnabled: _isMainButtonEnabled(),
                onMainAction: _getMainAction(),
                onBackAction: controller!.currentStep > 1 ? controller!.goToPreviousStep : null,
              )),
        ],
      ),
    );
  }

  bool _isMainButtonEnabled() {
    switch (controller!.currentStep) {
      case 1:
        return controller!.isEmailValid;
      case 2:
        return controller!.isCodeValid;
      case 3:
        return controller!.isUniversityStepValid;
      default:
        return false;
    }
  }

  VoidCallback? _getMainAction() {
    switch (controller!.currentStep) {
      case 1:
        return controller!.sendVerificationEmail;
      case 2:
        return controller!.validateVerificationCode;
      case 3:
        return controller!.completeEduVerification;
      default:
        return null;
    }
  }
}
