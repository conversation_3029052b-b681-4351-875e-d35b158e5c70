import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';

import 'constants/creator_request_constants.dart';
import 'widgets/next_steps_section.dart';
import 'widgets/status_action_buttons.dart';
import 'widgets/status_header.dart';
import 'widgets/status_timeline.dart';

class CreatorRequestStatus extends StatefulWidget {
  const CreatorRequestStatus({super.key});

  @override
  State<CreatorRequestStatus> createState() => _CreatorRequestStatusState();
}

class _CreatorRequestStatusState extends State<CreatorRequestStatus> {
  @override
  Widget build(BuildContext context) {
    return IaScaffold.noSearch(
      title: CreatorRequestConstants.statusTitle,
      showBackButton: true,
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppDimensions.padding16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            StatusHeader(statusIcon: _getStatusIcon()),
            const SizedBox(height: AppDimensions.padding32),
            const StatusTimeline(),
            const SizedBox(height: AppDimensions.padding32),
            const NextStepsSection(),
            const SizedBox(height: AppDimensions.padding32),
            const StatusActionButtons(),
          ],
        ),
      ),
    );
  }

  IconData _getStatusIcon() {
    return Icons.check_circle;
  }
}
