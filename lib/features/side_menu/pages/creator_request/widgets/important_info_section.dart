import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import '../constants/creator_request_constants.dart';

class ImportantInfoSection extends StatelessWidget {
  const ImportantInfoSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.padding16),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: Colors.blue.shade600,
                size: 20,
              ),
              const SizedBox(width: AppDimensions.padding8),
              Text(
                CreatorRequestConstants.step4ImportantInfoTitle,
                style: AppTextStyles.size16Bold.copyWith(
                  color: Colors.blue.shade600,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.padding12),
          Text(
            CreatorRequestConstants.step4ImportantInfoContent,
            style: AppTextStyles.size14Regular.copyWith(
              color: Colors.blue.shade700,
            ),
          ),
        ],
      ),
    );
  }
}
