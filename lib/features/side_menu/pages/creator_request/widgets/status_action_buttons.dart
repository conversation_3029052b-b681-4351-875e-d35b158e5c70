import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import '../constants/creator_request_constants.dart';
import 'support_dialog.dart';

class StatusActionButtons extends StatelessWidget {
  const StatusActionButtons({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: () {
              Get.offAllNamed('/app_navigation');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              padding:
                  const EdgeInsets.symmetric(vertical: AppDimensions.padding16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              ),
            ),
            child: Text(
              CreatorRequestConstants.profileButton,
              style: AppTextStyles.size16Bold.copyWith(
                color: Colors.white,
              ),
            ),
          ),
        ),
        const SizedBox(height: AppDimensions.padding12),
        SizedBox(
          width: double.infinity,
          child: OutlinedButton(
            onPressed: SupportDialog.show,
            style: OutlinedButton.styleFrom(
              padding:
                  const EdgeInsets.symmetric(vertical: AppDimensions.padding16),
              side: const BorderSide(color: AppColors.primary),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              ),
            ),
            child: Text(
              CreatorRequestConstants.supportButton,
              style: AppTextStyles.size16Bold.copyWith(
                color: AppColors.primary,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
