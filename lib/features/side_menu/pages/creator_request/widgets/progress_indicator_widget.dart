import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';

class ProgressIndicatorWidget extends StatelessWidget {
  final double progress;
  final String stepText;

  const ProgressIndicatorWidget({
    super.key,
    required this.progress,
    required this.stepText,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        LinearProgressIndicator(
          value: progress,
          backgroundColor: AppColors.grey300,
          valueColor: const AlwaysStoppedAnimation<Color>(AppColors.primary),
        ),
        const SizedBox(height: 20),
        Text(
          stepText,
          style: AppTextStyles.size12Regular.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }
}
