import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import '../models/summary_section.dart';

class SummaryCard extends StatelessWidget {
  final SummarySection section;

  const SummaryCard({
    super.key,
    required this.section,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppDimensions.padding16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            section.title,
            style: AppTextStyles.size16Bold,
          ),
          const SizedBox(height: AppDimensions.padding16),
          ...section.items.map((item) => _buildSummaryItem(item)),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(SummaryItem item) {
    if (item.value.isEmpty) return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.only(bottom: AppDimensions.padding8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            item.label,
            style: AppTextStyles.size14Medium.copyWith(
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            item.value,
            style: AppTextStyles.size14Regular,
            maxLines: item.isLong ? null : 1,
            overflow: item.isLong ? null : TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}
