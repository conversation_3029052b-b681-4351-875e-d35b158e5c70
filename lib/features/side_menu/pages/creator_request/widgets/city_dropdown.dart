import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import '../constants/creator_request_constants.dart';

class CityDropdown extends StatelessWidget {
  final String? selectedCity;
  final Function(String?) onChanged;
  final String? Function(String?)? validator;

  const CityDropdown({
    super.key,
    required this.selectedCity,
    required this.onChanged,
    this.validator,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.grey100,
        borderRadius: BorderRadius.circular(12),
      ),
      child: DropdownButtonFormField<String>(
        value: selectedCity,
        decoration: InputDecoration(
          labelText: CreatorRequestConstants.cityLabel,
          labelStyle: AppTextStyles.size14Regular.copyWith(
            color: AppColors.textSecondary,
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 16,
          ),
        ),
        items: CreatorRequestConstants.turkishCities.map((String city) {
          return DropdownMenuItem<String>(
            value: city,
            child: Text(
              city,
              style: AppTextStyles.size16Regular.copyWith(
                color: AppColors.textPrimary,
              ),
            ),
          );
        }).toList(),
        onChanged: onChanged,
        validator: validator,
      ),
    );
  }
}
