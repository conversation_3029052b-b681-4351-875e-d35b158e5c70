import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import '../constants/creator_request_constants.dart';
import '../models/timeline_item.dart';
import 'timeline_item_widget.dart';

class StatusTimeline extends StatelessWidget {
  const StatusTimeline({super.key});

  @override
  Widget build(BuildContext context) {
    final timelineItems = [
      const TimelineItem(
        title: CreatorRequestConstants.timelineStep1Title,
        description: CreatorRequestConstants.timelineStep1Description,
        isCompleted: true,
        isActive: false,
        time: CreatorRequestConstants.timelineStep1Time,
      ),
      const TimelineItem(
        title: CreatorRequestConstants.timelineStep2Title,
        description: CreatorRequestConstants.timelineStep2Description,
        isCompleted: false,
        isActive: true,
        time: CreatorRequestConstants.timelineStep2Time,
      ),
      const TimelineItem(
        title: CreatorRequestConstants.timelineStep3Title,
        description: CreatorRequestConstants.timelineStep3Description,
        isCompleted: false,
        isActive: false,
        time: CreatorRequestConstants.timelineStep3Time,
      ),
      const TimelineItem(
        title: CreatorRequestConstants.timelineStep4Title,
        description: CreatorRequestConstants.timelineStep4Description,
        isCompleted: false,
        isActive: false,
        time: CreatorRequestConstants.timelineStep4Time,
      ),
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          CreatorRequestConstants.timelineTitle,
          style: AppTextStyles.size20Bold,
        ),
        const SizedBox(height: AppDimensions.padding16),
        ...timelineItems.map((item) => TimelineItemWidget(item: item)),
      ],
    );
  }
}
