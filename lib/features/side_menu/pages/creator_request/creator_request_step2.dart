import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/side_menu/controllers/creator_request_controller.dart'; 

import 'constants/creator_request_constants.dart';
import 'widgets/progress_indicator_widget.dart';

class CreatorRequestStep2 extends StatelessWidget {
  const CreatorRequestStep2({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<CreatorRequestController>();

    return IaScaffold.noSearch(
      title: CreatorRequestConstants.appTitle,
      showBackButton: true,
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ProgressIndicatorWidget(
              progress: controller.progressPercentage,
              stepText: '2/3 Adım',
            ),
            const SizedBox(height: 10),
            Text(
              CreatorRequestConstants.step2Title,
              style: AppTextStyles.size20Bold.copyWith(
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 20),
            Text(
              CreatorRequestConstants.step2Description,
              style: AppTextStyles.size14Regular.copyWith(
                color: AppColors.textSecondary,
                height: 1.5,
              ),
            ),
            const SizedBox(height: 30),

            // Text field
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color: AppColors.grey100,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: AppColors.grey300,
                    width: 1,
                  ),
                ),
                child: TextField(
                  controller: controller.descriptionController,
                  maxLines: null,
                  expands: true,
                  textAlignVertical: TextAlignVertical.top,
                  style: AppTextStyles.size14Regular.copyWith(
                    color: AppColors.textPrimary,
                  ),
                  decoration: InputDecoration(
                    hintText: CreatorRequestConstants.step2Placeholder,
                    hintStyle: AppTextStyles.size14Regular.copyWith(
                      color: AppColors.textSecondary,
                    ),
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.all(16),
                  ),
                  onChanged: (value) {
                    controller.description.value = value;
                  },
                ),
              ),
            ),
            const SizedBox(height: 20),

            // Character count
            Obx(() => Text(
                  '${controller.description.value.length}/${CreatorRequestConstants.minDescriptionLength}',
                  style: AppTextStyles.size12Regular.copyWith(
                    color: controller.description.value.length >=
                            CreatorRequestConstants.minDescriptionLength
                        ? AppColors.success
                        : AppColors.textSecondary,
                  ),
                )),
            const SizedBox(height: 20),

            // Continue button
            SizedBox(
              width: double.infinity,
              child: Obx(() => ElevatedButton(
                    onPressed: controller.description.value.length >=
                            CreatorRequestConstants.minDescriptionLength
                        ? () => controller.goToStep3()
                        : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: controller.description.value.length >=
                              CreatorRequestConstants.minDescriptionLength
                          ? AppColors.primary
                          : AppColors.grey400,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 0,
                    ),
                    child: Text(
                      controller.description.value.length >=
                              CreatorRequestConstants.minDescriptionLength
                          ? CreatorRequestConstants.continueButton
                          : CreatorRequestConstants.minCharacterWarning,
                      style: AppTextStyles.size16Bold.copyWith(
                        color: Colors.white,
                      ),
                    ),
                  )),
            ),
          ],
        ),
      ),
    );
  }
}
