import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/side_menu/controllers/creator_request_controller.dart';

import 'constants/creator_request_constants.dart';
import 'widgets/important_info_section.dart';
import 'widgets/step_progress_indicator.dart';

class CreatorRequestStep4 extends StatefulWidget {
  const CreatorRequestStep4({super.key});

  @override
  State<CreatorRequestStep4> createState() => _CreatorRequestStep4State();
}

class _CreatorRequestStep4State extends State<CreatorRequestStep4> {
  CreatorRequestController? controller;

  @override
  void initState() {
    super.initState();
    controller = Get.find<CreatorRequestController>();
  }

  @override
  Widget build(BuildContext context) {
    return IaScaffold.noSearch(
      title: CreatorRequestConstants.step4Title,
      body: Obx(() => SingleChildScrollView(
            padding: const EdgeInsets.all(AppDimensions.padding16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const StepProgressIndicator(currentStep: 4, totalSteps: 4),
                const SizedBox(height: AppDimensions.padding32),
                Text(
                  CreatorRequestConstants.step4SummaryTitle,
                  style: AppTextStyles.size24Bold,
                ),
                const SizedBox(height: AppDimensions.padding8),
                Text(
                  CreatorRequestConstants.step4SummaryDescription,
                  style: AppTextStyles.size16Regular.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
                const SizedBox(height: AppDimensions.padding32),
                _buildSummarySection(),
                const SizedBox(height: AppDimensions.padding32),
                const ImportantInfoSection(),
                const SizedBox(height: AppDimensions.padding32),
                _buildSubmitButton(),
              ],
            ),
          )),
    );
  }

  Widget _buildSummarySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSummaryCard(
          title: CreatorRequestConstants.step4PersonalInfoTitle,
          children: [
            _buildSummaryItem('Ad Soyad', controller!.fullNameController.text),
            _buildSummaryItem('E-posta', controller!.emailController.text),
            _buildSummaryItem('Telefon', controller!.phoneController.text),
            _buildSummaryItem('Doğum Tarihi', controller!.birthDateController.text),
            _buildSummaryItem('Şehir', controller!.cityController.text),
          ],
        ),
        const SizedBox(height: AppDimensions.padding16),
        _buildSummaryCard(
          title: CreatorRequestConstants.step4ExperienceInfoTitle,
          children: [
            _buildSummaryItem('Deneyim', controller!.experienceController.text),
            _buildSummaryItem('Motivasyon', controller!.motivationController.text, isLong: true),
            _buildSummaryItem('Biyografi', controller!.bioController.text, isLong: true),
          ],
        ),
        const SizedBox(height: AppDimensions.padding16),
        _buildSummaryCard(
          title: CreatorRequestConstants.step4VerificationInfoTitle,
          children: [
            _buildSummaryItem('TC Kimlik No', controller!.tcNoController.text),
            if (controller!.instagramController.text.isNotEmpty)
              _buildSummaryItem('Instagram', '@${controller!.instagramController.text}'),
            if (controller!.twitterController.text.isNotEmpty)
              _buildSummaryItem('Twitter', '@${controller!.twitterController.text}'),
            if (controller!.tiktokController.text.isNotEmpty)
              _buildSummaryItem('TikTok', '@${controller!.tiktokController.text}'),
            if (controller!.youtubeController.text.isNotEmpty)
              _buildSummaryItem('YouTube', controller!.youtubeController.text),
            if (controller!.websiteController.text.isNotEmpty)
              _buildSummaryItem('Website', controller!.websiteController.text),
          ],
        ),
      ],
    );
  }

  Widget _buildSummaryCard({
    required String title,
    required List<Widget> children,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppDimensions.padding16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: AppTextStyles.size16Bold,
          ),
          const SizedBox(height: AppDimensions.padding16),
          ...children,
        ],
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value, {bool isLong = false}) {
    if (value.isEmpty) return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.only(bottom: AppDimensions.padding8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: AppTextStyles.size14Medium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: AppTextStyles.size14Regular,
            maxLines: isLong ? null : 1,
            overflow: isLong ? null : TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildSubmitButton() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: OutlinedButton(
                onPressed: () => Get.back(),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: AppDimensions.padding16),
                  side: const BorderSide(color: AppColors.primary),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  ),
                ),
                child: Text(
                  CreatorRequestConstants.backButton,
                  style: AppTextStyles.size16Bold.copyWith(
                    color: AppColors.primary,
                  ),
                ),
              ),
            ),
            const SizedBox(width: AppDimensions.padding16),
            Expanded(
              flex: 2,
              child: ElevatedButton(
                onPressed: controller!.isLoading() ? null : () => controller!.submitCreatorRequest(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  padding: const EdgeInsets.symmetric(vertical: AppDimensions.padding16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  ),
                ),
                child: controller!.isLoading()
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Text(
                        CreatorRequestConstants.submitFinalButton,
                        style: AppTextStyles.size16Bold.copyWith(
                          color: Colors.white,
                        ),
                      ),
              ),
            ),
          ],
        ),
        const SizedBox(height: AppDimensions.padding16),
        Text(
          CreatorRequestConstants.step4AgreementText,
          style: AppTextStyles.size12Regular.copyWith(
            color: AppColors.textSecondary,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
