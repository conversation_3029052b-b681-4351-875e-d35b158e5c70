import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/features/side_menu/constants/creator_request_constants.dart';

class CreatorRequestHeaderSection extends StatelessWidget {
  const CreatorRequestHeaderSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.padding16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.primary, AppColors.primary.withValues(alpha: 0.8)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(CreatorRequestConstants.iconPadding),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: CreatorRequestConstants.headerOpacity),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                ),
                child: const Icon(
                  Icons.star,
                  color: Colors.white,
                  size: CreatorRequestConstants.iconSize,
                ),
              ),
              const SizedBox(width: AppDimensions.padding16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      CreatorRequestConstants.headerTitle,
                      style: AppTextStyles.size20Bold.copyWith(
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: CreatorRequestConstants.headerSpacing),
                    Text(
                      CreatorRequestConstants.headerSubtitle,
                      style: AppTextStyles.size14Regular.copyWith(
                        color: Colors.white.withValues(alpha: CreatorRequestConstants.descriptionOpacity),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.padding16),
          Text(
            CreatorRequestConstants.headerDescription,
            style: AppTextStyles.size14Regular.copyWith(
              color: Colors.white.withValues(alpha: CreatorRequestConstants.descriptionOpacity),
            ),
          ),
        ],
      ),
    );
  }
}
