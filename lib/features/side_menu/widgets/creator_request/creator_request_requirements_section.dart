import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/features/side_menu/constants/creator_request_constants.dart';

class CreatorRequestRequirementsSection extends StatelessWidget {
  final Map<String, String> requirementDescriptions;
  final Map<String, bool> levelRequirements;
  final VoidCallback onShowRequirementsDialog;

  const CreatorRequestRequirementsSection({
    super.key,
    required this.requirementDescriptions,
    required this.levelRequirements,
    required this.onShowRequirementsDialog,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              CreatorRequestConstants.requirementsTitle,
              style: AppTextStyles.size16Bold,
            ),
            TextButton(
              onPressed: onShowRequirementsDialog,
              child: Text(
                CreatorRequestConstants.requirementsDetailsButton,
                style: AppTextStyles.size14Regular.copyWith(
                  color: AppColors.primary,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: AppDimensions.padding8),
        ...requirementDescriptions.entries
            .map((entry) => Padding(
                  padding: const EdgeInsets.only(bottom: CreatorRequestConstants.requirementItemSpacing),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(
                        levelRequirements[entry.key] == true
                            ? Icons.check_circle
                            : Icons.radio_button_unchecked,
                        color: levelRequirements[entry.key] == true ? Colors.green : Colors.grey,
                        size: CreatorRequestConstants.requirementIconSize,
                      ),
                      const SizedBox(width: CreatorRequestConstants.requirementItemSpacing),
                      Expanded(
                        child: Text(
                          entry.value,
                          style: AppTextStyles.size14Regular.copyWith(
                            color: levelRequirements[entry.key] == true
                                ? AppColors.textPrimary
                                : AppColors.textSecondary,
                          ),
                        ),
                      ),
                    ],
                  ),
                ))
            .toList(),
      ],
    );
  }
}
