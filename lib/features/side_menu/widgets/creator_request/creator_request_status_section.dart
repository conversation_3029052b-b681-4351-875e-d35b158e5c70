import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/features/side_menu/constants/creator_request_constants.dart';

class CreatorRequestStatusSection extends StatelessWidget {
  final bool isApplicationSubmitted;
  final bool canApply;
  final String applicationStatus;

  const CreatorRequestStatusSection({
    super.key,
    required this.isApplicationSubmitted,
    required this.canApply,
    required this.applicationStatus,
  });

  @override
  Widget build(BuildContext context) {
    final statusColor = _getStatusColor();
    final statusIcon = _getStatusIcon();

    return Container(
      padding: const EdgeInsets.all(AppDimensions.padding16),
      decoration: BoxDecoration(
        color: statusColor.withValues(alpha: CreatorRequestConstants.statusBackgroundOpacity),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: statusColor,
          width: CreatorRequestConstants.statusBorderWidth,
        ),
      ),
      child: Row(
        children: [
          Icon(
            statusIcon,
            color: statusColor,
            size: CreatorRequestConstants.statusIconSize,
          ),
          const SizedBox(width: AppDimensions.padding16),
          Expanded(
            child: Text(
              applicationStatus,
              style: AppTextStyles.size14Regular.copyWith(
                color: _getTextColor(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor() {
    if (isApplicationSubmitted) {
      return Colors.green;
    } else if (canApply) {
      return Colors.blue;
    } else {
      return Colors.orange;
    }
  }

  IconData _getStatusIcon() {
    if (isApplicationSubmitted) {
      return Icons.check_circle;
    } else if (canApply) {
      return Icons.info;
    } else {
      return Icons.star;
    }
  }

  Color _getTextColor() {
    if (isApplicationSubmitted) {
      return Colors.green.shade700;
    } else if (canApply) {
      return Colors.blue.shade700;
    } else {
      return Colors.orange.shade700;
    }
  }
}
