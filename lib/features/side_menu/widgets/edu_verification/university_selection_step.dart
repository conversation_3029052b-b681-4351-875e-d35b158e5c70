import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/features/side_menu/constants/profile_constants.dart';

class UniversitySelectionStep extends StatelessWidget {
  final List<Map<String, String>> universities;
  final String? selectedUniversityId;
  final Function(String) onUniversitySelected;

  const UniversitySelectionStep({
    Key? key,
    required this.universities,
    required this.selectedUniversityId,
    required this.onUniversitySelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          ProfileConstants.universitySelectionText,
          style: AppTextStyles.size16Medium,
        ),
        const SizedBox(height: AppDimensions.padding8),
        Text(
          ProfileConstants.selectUniversityText,
          style: AppTextStyles.size14Regular.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: AppDimensions.padding16),
        Column(
          children: universities.map((university) {
            final isSelected = selectedUniversityId == university['id'];
            final title = university['name'] ?? '';
            final description = university['description'] ?? '';

            return Padding(
              padding: const EdgeInsets.only(bottom: AppDimensions.padding8),
              child: GestureDetector(
                onTap: () => onUniversitySelected(university['id'] ?? ''),
                child: Container(
                  padding: const EdgeInsets.all(AppDimensions.padding16),
                  decoration: BoxDecoration(
                    color: isSelected ? AppColors.primary.withOpacity(0.1) : AppColors.grey50,
                    borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                    border: Border.all(
                      color: isSelected ? AppColors.primary : AppColors.grey200,
                      width: isSelected ? 2 : 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      Container(
                        width: 20,
                        height: 20,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: isSelected ? AppColors.primary : AppColors.grey200,
                          border: Border.all(
                            color: isSelected ? AppColors.primary : AppColors.grey400,
                            width: 2,
                          ),
                        ),
                        child: isSelected
                            ? const Icon(
                                Icons.check,
                                size: 12,
                                color: AppColors.white,
                              )
                            : null,
                      ),
                      const SizedBox(width: AppDimensions.padding12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              title,
                              style: AppTextStyles.size14Medium.copyWith(
                                color: isSelected ? AppColors.primary : AppColors.textPrimary,
                              ),
                            ),
                            if (description.isNotEmpty) ...[
                              const SizedBox(height: 2),
                              Text(
                                description,
                                style: AppTextStyles.size12Regular.copyWith(
                                  color: AppColors.textSecondary,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }
}
