import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/features/side_menu/constants/profile_constants.dart';

class VerificationCodeStep extends StatelessWidget {
  final String email;
  final TextEditingController verificationCodeController;
  final bool isCodeValid;
  final int resendTimer;
  final VoidCallback onChanged;
  final VoidCallback onResendCode;

  const VerificationCodeStep({
    Key? key,
    required this.email,
    required this.verificationCodeController,
    required this.isCodeValid,
    required this.resendTimer,
    required this.onChanged,
    required this.onResendCode,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(AppDimensions.padding16),
          decoration: BoxDecoration(
            color: AppColors.grey50,
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            border: Border.all(color: AppColors.grey200),
          ),
          child: Row(
            children: [
              const Icon(
                Icons.email,
                color: AppColors.primary,
                size: 24,
              ),
              const SizedBox(width: AppDimensions.padding12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      ProfileConstants.codeSentText,
                      style: AppTextStyles.size14Medium,
                    ),
                    Text(
                      email,
                      style: AppTextStyles.size12Regular.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: AppDimensions.padding16),
        Text(
          '${ProfileConstants.verificationCodeLabel} *',
          style: AppTextStyles.size14Medium,
        ),
        const SizedBox(height: AppDimensions.padding8),
        Container(
          decoration: BoxDecoration(
            color: AppColors.grey100,
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            border: Border.all(
              color: isCodeValid ? AppColors.primary.withOpacity(0.3) : AppColors.grey300,
            ),
          ),
          child: TextField(
            controller: verificationCodeController,
            keyboardType: TextInputType.number,
            decoration: InputDecoration(
              hintText: ProfileConstants.verificationCodeHint,
              hintStyle: AppTextStyles.size16Regular.copyWith(
                color: AppColors.textSecondary,
              ),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.all(AppDimensions.padding16),
              suffixIcon: Icon(
                Icons.security,
                color: isCodeValid ? AppColors.primary : AppColors.grey500,
              ),
            ),
            style: AppTextStyles.size16Regular,
            onChanged: (_) => onChanged(),
          ),
        ),
        const SizedBox(height: AppDimensions.padding16),
        GestureDetector(
          onTap: resendTimer > 0 ? null : onResendCode,
          child: Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppDimensions.padding16,
              vertical: AppDimensions.padding8,
            ),
            decoration: BoxDecoration(
              color: resendTimer > 0 ? AppColors.grey200 : AppColors.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
              border: Border.all(
                color: resendTimer > 0 ? AppColors.grey300 : AppColors.primary.withOpacity(0.3),
              ),
            ),
            child: Text(
              resendTimer > 0 
                  ? ProfileConstants.getResendTimerText(resendTimer)
                  : ProfileConstants.resendCodeText,
              style: AppTextStyles.size12Medium.copyWith(
                color: resendTimer > 0 ? AppColors.textSecondary : AppColors.primary,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
