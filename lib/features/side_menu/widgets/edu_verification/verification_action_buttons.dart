import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/features/side_menu/constants/profile_constants.dart';

class VerificationActionButtons extends StatelessWidget {
  final int currentStep;
  final bool isLoading;
  final bool isMainButtonEnabled;
  final VoidCallback? onMainAction;
  final VoidCallback? onBackAction;

  const VerificationActionButtons({
    Key? key,
    required this.currentStep,
    required this.isLoading,
    required this.isMainButtonEnabled,
    required this.onMainAction,
    this.onBackAction,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: isMainButtonEnabled && !isLoading ? onMainAction : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: isMainButtonEnabled ? AppColors.primary : AppColors.grey300,
              padding: const EdgeInsets.symmetric(vertical: AppDimensions.padding16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              ),
            ),
            child: isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(AppColors.white),
                    ),
                  )
                : Text(
                    _getMainButtonText(),
                    style: AppTextStyles.size16Bold.copyWith(
                      color: AppColors.white,
                    ),
                  ),
          ),
        ),
        if (currentStep > 1 && onBackAction != null) ...[
          const SizedBox(height: AppDimensions.padding12),
          SizedBox(
            width: double.infinity,
            child: TextButton(
              onPressed: onBackAction,
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: AppDimensions.padding16),
              ),
              child: Text(
                ProfileConstants.backButtonText,
                style: AppTextStyles.size16Medium.copyWith(
                  color: AppColors.primary,
                ),
              ),
            ),
          ),
        ],
      ],
    );
  }

  String _getMainButtonText() {
    switch (currentStep) {
      case 1:
        return ProfileConstants.sendVerificationText;
      case 2:
        return ProfileConstants.validateCodeText;
      case 3:
        return ProfileConstants.completeVerificationText;
      default:
        return ProfileConstants.continueText;
    }
  }
}
