import 'package:flutter/material.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';

class GraduationStatusSelection extends StatelessWidget {
  final UserEduVerificationEnum? selectedGradStatus;
  final Function(UserEduVerificationEnum) onStatusSelected;

  const GraduationStatusSelection({
    Key? key,
    required this.selectedGradStatus,
    required this.onStatusSelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Mezuniyet Durumu *',
          style: AppTextStyles.size14Medium,
        ),
        const SizedBox(height: AppDimensions.padding8),
        Column(
          children: UserEduVerificationEnum.values
              .where((status) => status != UserEduVerificationEnum.unverified)
              .map((status) => _buildStatusOption(status))
              .toList(),
        ),
      ],
    );
  }

  Widget _buildStatusOption(UserEduVerificationEnum status) {
    final isSelected = selectedGradStatus == status;
    final statusInfo = _getStatusInfo(status);

    return Container(
      margin: const EdgeInsets.only(bottom: AppDimensions.padding8),
      child: GestureDetector(
        onTap: () => onStatusSelected(status),
        child: Container(
          padding: const EdgeInsets.all(AppDimensions.padding16),
          decoration: BoxDecoration(
            color: isSelected ? AppColors.primary.withValues(alpha: 0.1) : AppColors.grey100,
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            border: Border.all(
              color: isSelected ? AppColors.primary : AppColors.grey300,
            ),
          ),
          child: Row(
            children: [
              Container(
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: isSelected ? AppColors.primary : AppColors.white,
                  border: Border.all(
                    color: isSelected ? AppColors.primary : AppColors.grey400,
                    width: 2,
                  ),
                ),
                child: isSelected
                    ? const Icon(
                        Icons.check,
                        size: 12,
                        color: AppColors.white,
                      )
                    : null,
              ),
              const SizedBox(width: AppDimensions.padding12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      statusInfo.title,
                      style: AppTextStyles.size14Medium.copyWith(
                        color: isSelected ? AppColors.primary : AppColors.textPrimary,
                      ),
                    ),
                    Text(
                      statusInfo.description,
                      style: AppTextStyles.size12Regular.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  _StatusInfo _getStatusInfo(UserEduVerificationEnum status) {
    switch (status) {
      case UserEduVerificationEnum.student:
        return _StatusInfo('Öğrenci', 'Halen eğitim görüyorum');
      case UserEduVerificationEnum.grad:
        return _StatusInfo('Mezun', 'Mezun oldum');
      case UserEduVerificationEnum.unverified:
      default:
        return _StatusInfo('', '');
    }
  }
}

class _StatusInfo {
  final String title;
  final String description;

  _StatusInfo(this.title, this.description);
}
