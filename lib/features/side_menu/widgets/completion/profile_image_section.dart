import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/features/side_menu/constants/profile_constants.dart';

class ProfileImageSection extends StatelessWidget {
  final Rx<File?> profileImage;
  final RxString currentAvatarUrl;
  final VoidCallback onPickImage;

  const ProfileImageSection({
    Key? key,
    required this.profileImage,
    required this.currentAvatarUrl,
    required this.onPickImage,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        children: [
          GestureDetector(
            onTap: onPickImage,
            child: Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: AppColors.grey200,
                border: Border.all(
                  color: AppColors.grey300,
                  width: 2,
                ),
              ),
              child: Obx(() {
                if (profileImage.value != null) {
                  return ClipRRect(
                    borderRadius: BorderRadius.circular(60),
                    child: Image.file(
                      profileImage.value!,
                      fit: BoxFit.cover,
                    ),
                  );
                } else if (currentAvatarUrl.value.isNotEmpty) {
                  return ClipRRect(
                    borderRadius: BorderRadius.circular(60),
                    child: Image.network(
                      currentAvatarUrl.value,
                      fit: BoxFit.cover,
                      loadingBuilder: (context, child, loadingProgress) {
                        if (loadingProgress == null) return child;
                        return Center(
                          child: CircularProgressIndicator(
                            value: loadingProgress.expectedTotalBytes != null
                                ? loadingProgress.cumulativeBytesLoaded / loadingProgress.expectedTotalBytes!
                                : null,
                          ),
                        );
                      },
                      errorBuilder: (context, error, stackTrace) {
                        return _buildDefaultAvatar();
                      },
                    ),
                  );
                } else {
                  return _buildDefaultAvatar();
                }
              }),
            ),
          ),
          const SizedBox(height: AppDimensions.padding8),
          GestureDetector(
            onTap: onPickImage,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.camera_alt,
                  size: 16,
                  color: AppColors.primary,
                ),
                const SizedBox(width: 4),
                Text(
                  ProfileConstants.changePhotoText,
                  style: AppTextStyles.size14Medium.copyWith(
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDefaultAvatar() {
    return const Icon(
      Icons.person,
      size: 60,
      color: AppColors.grey500,
    );
  }
}
