import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';

class ProfileFormField extends StatelessWidget {
  final String label;
  final String? hint;
  final String? note;
  final bool isRequired;
  final bool isReadOnly;
  final TextEditingController? controller;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final VoidCallback? onTap;
  final Function(String)? onChanged;
  final String? value;

  const ProfileFormField({
    Key? key,
    required this.label,
    this.hint,
    this.note,
    this.isRequired = false,
    this.isReadOnly = false,
    this.controller,
    this.prefixIcon,
    this.suffixIcon,
    this.onTap,
    this.onChanged,
    this.value,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              label,
              style: AppTextStyles.size16Medium.copyWith(
                color: AppColors.textPrimary,
              ),
            ),
            if (isRequired) ...[
              const SizedBox(width: 4),
              Text(
                '*',
                style: AppTextStyles.size16Medium.copyWith(
                  color: Colors.red,
                ),
              ),
            ],
          ],
        ),
        const SizedBox(height: AppDimensions.padding8),
        if (isReadOnly && value != null)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(
              horizontal: AppDimensions.padding16,
              vertical: AppDimensions.padding12,
            ),
            decoration: BoxDecoration(
              color: AppColors.grey50,
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              border: Border.all(color: AppColors.grey300),
            ),
            child: Text(
              value!.isNotEmpty ? value! : 'Yükleniyor...',
              style: AppTextStyles.size16Regular.copyWith(
                color: value!.isNotEmpty ? AppColors.textSecondary : AppColors.grey500,
              ),
            ),
          )
        else if (onTap != null)
          GestureDetector(
            onTap: onTap,
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(
                horizontal: AppDimensions.padding16,
                vertical: AppDimensions.padding12,
              ),
              decoration: BoxDecoration(
                color: AppColors.grey100,
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                border: Border.all(color: AppColors.grey300),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    value ?? hint ?? '',
                    style: AppTextStyles.size16Regular.copyWith(
                      color: (value?.isEmpty ?? true) ? AppColors.textSecondary : AppColors.textPrimary,
                    ),
                  ),
                  if (suffixIcon != null) suffixIcon!,
                ],
              ),
            ),
          )
        else
          Container(
            decoration: BoxDecoration(
              color: AppColors.grey100,
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              border: Border.all(color: AppColors.grey300),
            ),
            child: TextField(
              controller: controller,
              style: AppTextStyles.size16Regular.copyWith(
                color: AppColors.textPrimary,
              ),
              decoration: InputDecoration(
                hintText: hint,
                hintStyle: AppTextStyles.size16Regular.copyWith(
                  color: AppColors.textSecondary,
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: AppDimensions.padding16,
                  vertical: AppDimensions.padding12,
                ),
                prefixIcon: prefixIcon,
                suffixIcon: suffixIcon,
              ),
              onChanged: onChanged,
            ),
          ),
        if (note != null) ...[
          const SizedBox(height: 4),
          Text(
            note!,
            style: AppTextStyles.size12Regular.copyWith(
              color: AppColors.grey500,
            ),
          ),
        ],
      ],
    );
  }
}
