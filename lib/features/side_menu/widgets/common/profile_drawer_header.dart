import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/features/side_menu/constants/drawer_constants.dart';
import 'package:ivent_app/features/side_menu/controllers/profile_side_menu_controller.dart';

class ProfileDrawerHeader extends StatelessWidget {
  const ProfileDrawerHeader({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<ProfileSideMenuController>();
    final user = controller.sessionUser;

    return Container(
      padding: const EdgeInsets.all(AppDimensions.padding20),
      child: Column(
        children: [
          CircleAvatar(
            radius: 40,
            backgroundImage: user.sessionAvatarUrl != null ? NetworkImage(user.sessionAvatarUrl!) : null,
            backgroundColor: AppColors.lightGrey,
          ),
          const SizedBox(height: AppDimensions.padding12),
          Text(
            '@${user.sessionUsername}',
            style: AppTextStyles.size16Bold,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppDimensions.padding4),
          InkWell(
            onTap: controller.onAccountManagementTap,
            child: Text(
              DrawerConstants.accountManagementText,
              style: AppTextStyles.size14Regular.copyWith(
                color: AppColors.primary,
                decoration: TextDecoration.underline,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }
}
