import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/index.dart';
import 'package:ivent_app/features/side_menu/constants/drawer_constants.dart';
import 'package:ivent_app/features/side_menu/controllers/profile_side_menu_controller.dart';

class ProfileDrawerActionButtons extends StatelessWidget {
  const ProfileDrawerActionButtons({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<ProfileSideMenuController>();

    return Column(
      children: [
        if (controller.showEduVerification)
          Container(
            margin: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
            child: _buildActionButton(
              icon: Icons.email,
              title: DrawerConstants.eduVerificationTitle,
              subtitle: DrawerConstants.eduVerificationSubtitle,
              onTap: controller.onEduVerificationTap,
              backgroundColor: AppColors.darkGrey,
            ),
          ),
        if (controller.showEduVerification) const SizedBox(height: AppDimensions.padding12),
        if (controller.showCreatorRequest)
          Container(
            margin: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
            child: _buildActionButton(
              icon: Icons.star,
              title: DrawerConstants.creatorRequestTitle,
              subtitle: DrawerConstants.creatorRequestSubtitle,
              onTap: controller.onCreatorRequestTap,
              backgroundColor: DrawerConstants.creatorButtonColor,
            ),
          ),
      ],
    );
  }

  Widget _buildActionButton({
    required dynamic icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    required Color backgroundColor,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        ),
        child: Padding(
          padding: const EdgeInsets.all(AppDimensions.padding16),
          child: Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: AppColors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                ),
                child: icon is IconData
                    ? Icon(icon, color: AppColors.white, size: 24)
                    : IaSvgIcon(
                        iconPath: icon,
                        iconSize: 24,
                        iconColor: AppColors.white,
                      ),
              ),
              const SizedBox(width: AppDimensions.padding12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      title,
                      style: AppTextStyles.size14Bold.copyWith(color: AppColors.white),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: AppDimensions.padding4),
                    Text(
                      subtitle,
                      style: AppTextStyles.size12Regular.copyWith(
                        color: AppColors.white.withOpacity(0.8),
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
