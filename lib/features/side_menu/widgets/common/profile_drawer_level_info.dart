import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/features/side_menu/controllers/profile_side_menu_controller.dart';

class ProfileDrawerLevelInfo extends StatelessWidget {
  const ProfileDrawerLevelInfo({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<ProfileSideMenuController>();

    return GestureDetector(
      onTap: controller.onLevelInfoTap,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
        padding: const EdgeInsets.all(AppDimensions.padding16),
        decoration: BoxDecoration(
          color: AppColors.primary.withOpacity(0.1),
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          border: Border.all(color: AppColors.primary.withOpacity(0.3)),
        ),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: AppColors.primary,
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
              ),
              child: const Icon(
                Icons.check_circle,
                color: AppColors.white,
                size: 24,
              ),
            ),
            const SizedBox(width: AppDimensions.padding12),
            Expanded(
              child: Obx(() => Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        controller.levelText,
                        style: AppTextStyles.size14Bold,
                      ),
                      const SizedBox(height: AppDimensions.padding4),
                      Text(
                        controller.levelDescription,
                        style: AppTextStyles.size12Regular.copyWith(color: AppColors.textSecondary),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  )),
            ),
          ],
        ),
      ),
    );
  }
}
