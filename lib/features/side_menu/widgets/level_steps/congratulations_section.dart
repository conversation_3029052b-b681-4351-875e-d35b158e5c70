import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/features/side_menu/constants/profile_constants.dart';

class CongratulationsSection extends StatelessWidget {
  const CongratulationsSection({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.padding20),
      decoration: BoxDecoration(
        color: AppColors.success.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(color: AppColors.success.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          const Icon(
            Icons.celebration,
            color: AppColors.success,
            size: 48,
          ),
          const SizedBox(height: AppDimensions.padding16),
          Text(
            ProfileConstants.congratulationsText,
            style: AppTextStyles.size16Bold.copyWith(
              color: AppColors.success,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
