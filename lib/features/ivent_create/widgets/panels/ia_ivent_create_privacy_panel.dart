// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:ivent_app/api/api.dart';
// import 'package:ivent_app/core/constants/app_colors.dart';
// import 'package:ivent_app/core/widgets/composite/tiles/ia_list_tile.dart';
// import 'package:ivent_app/core/widgets/foundation/containers/ia_rounded_container.dart';
// import 'package:ivent_app/core/widgets/foundation/graphics/ia_divider.dart';
// import 'package:ivent_app/features/ivent_create/controllers/ivent_create_controller.dart';

// class IaIventCreatePrivacyPanel extends StatefulWidget {
//   const IaIventCreatePrivacyPanel({super.key});

//   @override
//   State<IaIventCreatePrivacyPanel> createState() => _IaIventCreatePrivacyPanelState();
// }

// class _IaIventCreatePrivacyPanelState extends State<IaIventCreatePrivacyPanel> {
//   final IventCreateController _controller = Get.find();

//   @override
//   Widget build(BuildContext context) {
//     return SingleChildScrollView(
//       child: Obx(() {
//         return Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             const IaDivider(margin: EdgeInsets.symmetric(vertical: 8)),
//             IaListTile.withIconData(
//               onTap: () => _controller.formController.selectedIventPrivacy = IventPrivacyEnum.public,
//               margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 20),
//               iconData: Icons.supervised_user_circle_outlined,
//               title: 'Herkes',
//               subtitle: 'Etkinliği herkesle paylaş',
//             ),
//             IaListTile.withIconData(
//               onTap: () => _controller.formController.selectedIventPrivacy = IventPrivacyEnum.friends,
//               margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 20),
//               iconData: Icons.group_outlined,
//               title: 'Arkadaşlar',
//               subtitle: 'Bla bla TODO',
//             ),
//             IaListTile.withIconData(
//               onTap: () => _controller.formController.selectedIventPrivacy = IventPrivacyEnum.edu,
//               margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 20),
//               iconData: Icons.group_outlined,
//               title: 'Sadece Üniversite Öğrencileri',
//               subtitle: 'Tüm üniversite öğrencileriyle paylaş',
//             ),
//             IaListTile.withIconData(
//               onTap: () => _controller.formController.selectedIventPrivacy = IventPrivacyEnum.selectedEdu,
//               margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 20),
//               iconData: Icons.group_outlined,
//               title: 'Seçili Üniversiteler',
//               subtitle: 'Seçilen üniversitelerin öğrencileri ile paylaş',
//               trailing: const Icon(Icons.keyboard_arrow_right),
//             ),
//             IaRoundedContainer(
//               onTap: () => _controller.submissionController.closePanel(),
//               margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 20),
//               color: AppColors.primary,
//               text: 'Bitti',
//             )
//           ],
//         );
//       }),
//     );
//   }
// }
