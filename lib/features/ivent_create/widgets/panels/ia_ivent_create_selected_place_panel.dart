// import 'package:flutter/material.dart';
// import 'package:ivent_app/core/constants/app_assets.dart';
// import 'package:ivent_app/core/constants/app_colors.dart';
// import 'package:ivent_app/core/widgets/composite/tiles/ia_list_tile.dart';
// import 'package:ivent_app/core/widgets/foundation/buttons/ia_floating_action_button.dart';
// import 'package:ivent_app/core/widgets/specialized/ivent/ivent_create_buttons.dart';
// import 'package:ivent_app/features/ivent_create/controllers/ivent_create_controller.dart';
// import 'package:ivent_app/features/mapbox/models/ia_location_item.dart';

// class IaIventCreateSelectedPlacePanel extends StatelessWidget {
//   final IventCreateController controller;

//   const IaIventCreateSelectedPlacePanel({
//     super.key,
//     required this.controller,
//   });

//   @override
//   Widget build(BuildContext context) {
//     if (controller.state.selectedPlace == null) {
//       return const SizedBox.shrink();
//     }

//     final selectedPlace = controller.state.selectedPlace!;

//     return Column(
//       children: [
//         _buildPlaceInfo(selectedPlace),
//         _buildActionButton(),
//       ],
//     );
//   }

//   Widget _buildPlaceInfo(IaLocationItem selectedPlace) {
//     return IaListTile.withSvgIcon(
//       padding: const EdgeInsets.symmetric(horizontal: 20),
//       iconPath: AppAssets.mapPin,
//       iconColor: AppColors.primary,
//       title: selectedPlace.name,
//       subtitle: selectedPlace.address,
//       trailing: _buildClearButton(),
//     );
//   }

//   Widget _buildClearButton() {
//     return IventCreateButtons.closeButtonDark(
//       onTap: _handleClearSelection,
//     );
//   }

//   Widget _buildActionButton() {
//     return IaFloatingActionButton(
//       text: 'Konumu Seç ve Etkinliği Önizle',
//       onPressed: _handleProceedToPreview,
//       isEnabled: true,
//       isPrimary: false,
//     );
//   }

//   void _handleClearSelection() {
//     controller.mapController.clearSearch();
//   }

//   void _handleProceedToPreview() {
//     controller.goToPreviewPage();
//   }
// }
