import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/foundation/containers/ia_image_container.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_image_controller.dart';

class Suggestions extends GetView<IventCreateImageController> {
  const Suggestions({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: AppDimensions.padding20),
          child: Text('Senin için Seçtiğimiz Görseller', style: AppTextStyles.size16MediumTextSecondary),
        ),
        GridView.builder(
          padding: const EdgeInsets.only(bottom: 100),
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            childAspectRatio: 1,
            crossAxisSpacing: AppDimensions.padding16,
            mainAxisSpacing: AppDimensions.padding16,
          ),
          itemCount: controller.imageUrls.length,
          itemBuilder: (context, index) => Obx(() {
            final imageUrl = controller.imageUrls[index];
            final isSelected = controller.selectedImageUrl == imageUrl;
            return IaImageContainer.withImageUrl(
              onTap: () => controller.handleSelectedImageUrlChanged(imageUrl),
              roundness: AppDimensions.radiusL,
              margin: EdgeInsets.all(isSelected ? 0 : 2),
              imageUrl: imageUrl,
              borderColor: isSelected ? AppColors.secondary : null,
              borderWidth: isSelected ? 2 : null,
            );
          }),
        ),
      ],
    );
  }
}
