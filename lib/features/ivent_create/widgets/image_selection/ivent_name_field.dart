import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_image_controller.dart';

class IventNameField extends GetView<IventCreateImageController> {
  const IventNameField({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.maxFinite,
      child: TextField(
        controller: controller.textController,
        style: AppTextStyles.size32Bold,
        decoration: InputDecoration(
          hintText: 'Etkin<PERSON> İsmi',
          hintStyle: AppTextStyles.size32BoldTextSecondary,
          border: InputBorder.none,
        ),
        onChanged: controller.handleIventNameChanged,
      ),
    );
  }
}
