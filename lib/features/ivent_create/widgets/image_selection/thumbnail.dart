import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/foundation/containers/ia_image_container.dart';
import 'package:ivent_app/core/widgets/foundation/graphics/ia_svg_icon.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_image_controller.dart';

class Thumbnail extends GetView<IventCreateImageController> {
  const Thumbnail({super.key});

  @override
  Widget build(BuildContext context) {
    return IaImageContainer.withChild(
      onTap: controller.goToImageGalleryPage,
      margin: const EdgeInsets.only(top: AppDimensions.padding12),
      roundness: AppDimensions.radiusL,
      child: Obx(() {
        if (!controller.isFileSelected) return const _ThumbnailIcon();
        return ClipRRect(
          borderRadius: BorderRadius.circular(AppDimensions.radiusL),
          child: controller.isFileSelected
              ? Image.memory(controller.selectedImageFile!, fit: BoxFit.cover)
              : Image.network(controller.selectedImageUrl!, fit: BoxFit.cover),
        );
      }),
    );
  }
}

class _ThumbnailIcon extends StatelessWidget {
  const _ThumbnailIcon();

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        const IaSvgIcon(iconPath: AppAssets.image01, iconSize: 100, iconColor: AppColors.mediumGrey),
        const SizedBox(height: AppDimensions.padding12),
        Text(
          'Görseli galeriden yüklemek için tıklayınız',
          style: AppTextStyles.size14MediumTextSecondary,
        ),
      ],
    );
  }
}
