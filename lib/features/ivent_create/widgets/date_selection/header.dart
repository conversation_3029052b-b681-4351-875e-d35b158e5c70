import 'package:flutter/material.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/foundation/containers/ia_circle_avatar.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_form_controller.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_state_manager.dart';

class Header extends GetView<IventCreateFormController> {
  const Header({super.key});

  IventCreateSharedState get state => controller.state;

  @override
  Widget build(BuildContext context) {
    return Obx(() => Row(
          children: [
            IaCircleAvatar(imageUrl: state.selectedImageUrl.value, radius: 24),
            const SizedBox(width: AppDimensions.padding12),
            Expanded(child: Text(state.iventName.value, style: AppTextStyles.size32Bold, maxLines: 1)),
          ],
        ));
  }
}
