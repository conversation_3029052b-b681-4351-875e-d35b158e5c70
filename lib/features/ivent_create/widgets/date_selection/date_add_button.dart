import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_rounded_button.dart';
import 'package:ivent_app/core/widgets/foundation/graphics/ia_svg_icon.dart';
import 'package:ivent_app/features/ivent_create/widgets/date_selection/date_picker.dart';

class DateAddButton extends StatelessWidget {
  const DateAddButton({super.key});

  void _openDatePicker({int? index}) {
    Get.bottomSheet(
      const DatePicker(),
      isScrollControlled: true,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: IaRoundedButton(
        onTap: _openDatePicker,
        color: AppColors.transparent,
        text: '<PERSON><PERSON><PERSON>',
        textStyle: AppTextStyles.size16RegularTextSecondary,
        leading: const IaSvgIcon(iconPath: AppAssets.addPlus, iconColor: AppColors.darkGrey),
      ),
    );
  }
}
