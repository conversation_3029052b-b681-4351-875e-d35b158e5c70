import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/foundation/containers/ia_rounded_container.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_date_controller.dart';

class DatePicker extends GetView<IventCreateDateController> {
  final DateTime? initialDateTime;

  const DatePicker({
    super.key,
    this.initialDateTime,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 300,
      color: AppColors.white,
      child: Column(
        children: [
          _buildHeader(),
          Expanded(child: _buildDatePicker()),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return IaRoundedContainer(
      padding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.padding8,
        vertical: AppDimensions.padding8,
      ),
      border: const Border(
        bottom: BorderSide(color: AppColors.lightGrey),
      ),
      color: AppColors.white,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _buildCancelButton(),
          _buildApplyButton(),
        ],
      ),
    );
  }

  Widget _buildCancelButton() {
    return TextButton(
      onPressed: () => Get.back(),
      child: Text(
        'Vazgeç',
        style: AppTextStyles.size16Bold.copyWith(color: AppColors.error),
      ),
    );
  }

  Widget _buildApplyButton() {
    return TextButton(
      onPressed: controller.handleDateAccepted,
      child: Text(
        'Uygula',
        style: AppTextStyles.size16BoldPrimary,
      ),
    );
  }

  Widget _buildDatePicker() {
    final now = DateTime.now();
    return CupertinoDatePicker(
      minimumDate: now,
      initialDateTime: initialDateTime ?? now,
      mode: CupertinoDatePickerMode.dateAndTime,
      use24hFormat: true,
      onDateTimeChanged: controller.handleDateSelected,
    );
  }
}
