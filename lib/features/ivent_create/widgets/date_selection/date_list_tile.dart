import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_icon_button.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_rounded_button.dart';
import 'package:ivent_app/core/widgets/foundation/containers/ia_rounded_container.dart';
import 'package:ivent_app/features/ivent_create/widgets/date_selection/date_picker.dart';

class DateListTile extends StatelessWidget {
  final String iconPath;
  final String infoText;
  final DateTime? date;
  final VoidCallback? onTrailingTap;

  const DateListTile({
    super.key,
    required this.iconPath,
    required this.infoText,
    this.date,
    this.onTrailingTap,
  });

  void _openDatePicker({int? index}) {
    Get.bottomSheet(
      DatePicker(initialDateTime: date),
      isScrollControlled: true,
    );
  }

  @override
  Widget build(BuildContext context) {
    return IaRoundedContainer(
      onTap: _openDatePicker,
      margin: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
      padding: const EdgeInsets.all(20),
      roundness: AppDimensions.radiusS,
      color: AppColors.lightGrey,
      child: Row(
        children: [
          IaRoundedButton(
            color: AppColors.transparent,
            text: infoText,
            textStyle: AppTextStyles.size16RegularTextSecondary,
            leading: IaIconButton(
              onPressed: onTrailingTap,
              iconPath: AppAssets.calendar,
              iconColor: AppColors.darkGrey,
              iconSize: 20,
            ),
          ),
          const Spacer(),
          Text(
            date != null ? DateFormat('d MMMM yyyy  HH:mm').format(date!) : 'Seçiniz',
            style: date != null ? AppTextStyles.size16Regular : AppTextStyles.size16RegularTextSecondary,
          ),
        ],
      ),
    );
  }
}
