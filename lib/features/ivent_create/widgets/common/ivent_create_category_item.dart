import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_rounded_button.dart';
import 'package:ivent_app/core/widgets/foundation/graphics/ia_svg_icon.dart';

class IventCreateCategoryItem extends StatelessWidget {
  final EdgeInsetsGeometry? margin;
  final VoidCallback? onTap;
  final bool isSelected;
  final String text;
  final String? leadingIconPath;
  final String? trailingIconPath;

  const IventCreateCategoryItem({
    super.key,
    required this.isSelected,
    required this.text,
    this.margin,
    this.onTap,
    this.leadingIconPath,
    this.trailingIconPath,
  });

  @override
  Widget build(BuildContext context) {
    return IaRoundedButton(
      margin: margin,
      width: double.maxFinite,
      height: AppDimensions.buttonHeightLongBar,
      roundness: AppDimensions.buttonRadiusS,
      onTap: onTap,
      color: isSelected ? AppColors.secondary : AppColors.lightGrey,
      text: text,
      textStyle: AppTextStyles.buttonTextMedium.copyWith(color: isSelected ? AppColors.white : AppColors.textPrimary),
      leading: leadingIconPath != null ? IaSvgIcon(iconPath: leadingIconPath!) : null,
      trailing: trailingIconPath != null ? IaSvgIcon(iconPath: trailingIconPath!) : null,
    );
  }
}
