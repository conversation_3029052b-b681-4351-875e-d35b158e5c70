import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/widgets/composite/tiles/ia_list_tile.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_form_controller.dart';
import 'package:ivent_app/features/ivent_create/widgets/common/ivent_create_category_item.dart';
import 'package:ivent_app/shared/domain/entities/hobby.dart';

class IventCreateCategoryList extends GetView<IventCreateFormController> {
  final int categoriesLevel;

  const IventCreateCategoryList({
    super.key,
    required this.categoriesLevel,
  });

  @override
  Widget build(BuildContext context) {
    final categories = Hobby.filterHobbies(level: categoriesLevel);

    return ListView.separated(
      padding: const EdgeInsets.only(top: AppDimensions.padding12, bottom: 100),
      itemCount: categories.length,
      itemBuilder: (context, index) {
        final category = categories[index];
        final isSelected = controller.selectedCategory.value?.hobbyId == category.hobbyId;

        return IventCreateCategoryItem(
          isSelected: isSelected,
          text: category.hobbyName,
          onTap: () => controller.handleMainCategorySelected(category),
        );
      },
      separatorBuilder: IaListTile.separatorBuilder20,
    );
  }
}
