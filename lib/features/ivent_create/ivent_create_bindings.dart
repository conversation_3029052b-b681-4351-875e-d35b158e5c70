import 'package:get/get.dart';
import 'package:ivent_app/core/services/auth_service.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_date_controller.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_form_controller.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_image_controller.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_map_controller.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_state_manager.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_submission_controller.dart';

class IventCreateBindings implements Bindings {
  @override
  void dependencies() {
    final service = Get.find<AuthService>();
    final sharedState = Get.put(IventCreateSharedState());

    Get.lazyPut<IventCreateFormController>(() => IventCreateFormController(service, sharedState));
    Get.lazyPut<IventCreateImageController>(() => IventCreateImageController(service, sharedState));
    Get.lazyPut<IventCreateMapController>(() => IventCreateMapController(service, sharedState));
    Get.lazyPut<IventCreateDateController>(() => IventCreateDateController(service, sharedState));
    Get.lazyPut<IventCreateSubmissionController>(() => IventCreateSubmissionController(service, sharedState));
  }
}
