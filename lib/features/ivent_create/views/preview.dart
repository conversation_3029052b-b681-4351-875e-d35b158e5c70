import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:ivent_app/api/api.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/composite/tiles/ia_basic_info_tile.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_floating_action_button.dart';
import 'package:ivent_app/core/widgets/foundation/containers/ia_image_container.dart';
import 'package:ivent_app/core/widgets/foundation/containers/ia_rounded_container.dart';
import 'package:ivent_app/core/widgets/foundation/graphics/ia_svg_icon.dart';
import 'package:ivent_app/core/widgets/layout/navigation/ia_top_bar.dart';
import 'package:ivent_app/core/widgets/layout/panels/ia_bottom_panel.dart';
import 'package:ivent_app/core/widgets/layout/panels/ia_sliding_panel.dart';
import 'package:ivent_app/core/widgets/specialized/ivent/ivent_create_buttons.dart';
import 'package:ivent_app/core/widgets/specialized/ivent/ivent_detail_buttons.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_form_controller.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_image_controller.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_map_controller.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_submission_controller.dart';
import 'package:sliding_up_panel/sliding_up_panel.dart';

class Preview extends GetView<IventCreateSubmissionController> {
  const Preview({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(() {
        return IaSlidingPanel(
          panelController: controller.panelController,
          minHeight: 0,
          maxHeight: 650,
          defaultPanelState: PanelState.CLOSED,
          onPanelClosed: () => controller.isPanelVisible.value = false,
          onPanelOpened: () => controller.isPanelVisible.value = true,
          panel: IaBottomPanel(
            showSlideIndicator: true,
            // body: _panels[controller.selectedPanelIndex.value],
            body: Column(),
          ),
          body: const SafeArea(
            child: _PreviewBody(),
          ),
        );
      }),
    );
  }

  // List<Widget> get _panels => const [
  //       IaIventCreateRegisterTypePanel(),
  //       IaIventCreatePrivacyPanel(),
  //     ];
}

class _PreviewBody extends GetView<IventCreateSubmissionController> {
  const _PreviewBody();

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        const Column(
          children: [
            _TopBar(),
            Expanded(
              child: SingleChildScrollView(
                padding: EdgeInsets.only(bottom: 100),
                child: Column(
                  children: [
                    SizedBox(height: AppDimensions.padding12),
                    _PreviewContent(),
                  ],
                ),
              ),
            ),
          ],
        ),
        Align(
          alignment: Alignment.bottomCenter,
          child: IaFloatingActionButton(
            text: 'Kayıt Türü Seç ve Yayınla',
            onPressed: () => controller.openPanel(0),
            isEnabled: true,
            isPrimary: false,
            trailingIconPath: AppAssets.caretCircleRight,
          ),
        ),
        Obx(() {
          return Positioned.fill(
            child: AnimatedOpacity(
              opacity: controller.isPanelVisible.value ? 1.0 : 0.0,
              duration: const Duration(milliseconds: AppDimensions.animationNormal),
              child: IgnorePointer(
                ignoring: !controller.isPanelVisible.value,
                child: IaRoundedContainer(
                  onTap: controller.closePanel,
                  color: AppColors.black.withValues(alpha: 0.5),
                ),
              ),
            ),
          );
        }),
      ],
    );
  }
}

class _TopBar extends StatelessWidget {
  const _TopBar();

  @override
  Widget build(BuildContext context) {
    final authService = Get.find();
    return IaTopBar.basic(
      showDivider: false,
      child: IaBasicInfoTile.withImageUrl(
        avatarUrl: authService.sessionUser.sessionAvatarUrl,
        title: authService.sessionUser.sessionFullname,
        subtitle: 'Düzenle',
      ),
      trailing: const _PrivacyButton(),
    );
  }
}

class _PrivacyButton extends GetView<IventCreateFormController> {
  const _PrivacyButton();

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final (String text, String iconPath) = switch (controller.selectedIventPrivacy.value) {
        IventPrivacyEnum.friends => ('Arkadaşlar', AppAssets.usersGroup),
        IventPrivacyEnum.edu => ('Sadece Üniversite Öğrencileri', AppAssets.usersGroup),
        IventPrivacyEnum.selectedEdu => ('Seçili Üniversiteler', AppAssets.usersGroup),
        IventPrivacyEnum.public => ('Herkes', AppAssets.globe),
        _ => ('Herkes', AppAssets.globe),
      };

      return IventCreateButtons.privacyIvent(
        onTap: () {},
        text: text,
        iconPath: iconPath,
      );
    });
  }
}

class _PreviewContent extends StatelessWidget {
  const _PreviewContent();

  @override
  Widget build(BuildContext context) {
    return const Column(
      children: [
        _Thumbnail(),
        SizedBox(height: AppDimensions.padding20),
        _Description(),
        SizedBox(height: AppDimensions.padding20),
        _IventTags(),
      ],
    );
  }
}

class _Thumbnail extends GetView<IventCreateImageController> {
  const _Thumbnail();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
      child: Stack(
        children: [
          IaImageContainer.withImageUrl(
            imageUrl: controller.selectedImageUrl,
            roundness: AppDimensions.radiusS,
          ),
          const _ThumbnailOverlay(),
        ],
      ),
    );
  }
}

class _ThumbnailOverlay extends StatelessWidget {
  const _ThumbnailOverlay();

  @override
  Widget build(BuildContext context) {
    return const Positioned.fill(
      child: IaRoundedContainer(
        padding: EdgeInsets.all(AppDimensions.padding16),
        roundness: AppDimensions.radiusS,
        gradient: AppColors.gradientBlackL,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _IventTitle(),
            SizedBox(height: AppDimensions.padding8),
            _LocationName(),
            SizedBox(height: AppDimensions.padding8),
            _DateDisplay(),
            Spacer(),
            _ActionButton(),
          ],
        ),
      ),
    );
  }
}

class _IventTitle extends GetView<IventCreateFormController> {
  const _IventTitle();

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Text(
        controller.state.iventName.value,
        style: AppTextStyles.size32BoldWhite,
        maxLines: 1,
        softWrap: false,
      );
    });
  }
}

class _LocationName extends GetView<IventCreateMapController> {
  const _LocationName();

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Text(
        controller.state.selectedPlace.value?.name ?? '',
        style: AppTextStyles.size20BoldWhite,
        maxLines: 1,
        softWrap: false,
      );
    });
  }
}

class _DateDisplay extends GetView<IventCreateFormController> {
  const _DateDisplay();

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.state.dates.isEmpty) return const SizedBox.shrink();
      final formattedDate = DateFormat('d MMMM yyyy, HH:mm').format(controller.state.dates[0]);
      return Text(
        formattedDate,
        style: AppTextStyles.size20MediumWhite,
        maxLines: 1,
        softWrap: false,
      );
    });
  }
}

class _ActionButton extends GetView<IventCreateFormController> {
  const _ActionButton();

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return IventDetailButtons.iventDetailTag(
        onTap: () {},
        text: controller.selectedCategory.value?.hobbyName ?? '',
      );
    });
  }
}

class _Description extends StatelessWidget {
  const _Description();

  @override
  Widget build(BuildContext context) {
    final formController = Get.find<IventCreateFormController>();
    return Obx(() {
      return IaRoundedContainer(
        onTap: () => Get.toNamed('/description'), // Replace with proper navigation
        margin: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
        padding: const EdgeInsets.all(AppDimensions.padding20),
        roundness: AppDimensions.radiusS,
        color: AppColors.lightGrey,
        child: Row(
          children: [
            Text(
              formController.description.value ?? 'Açıklama Ekle (Opsiyonel)',
              style: formController.description.value != null
                  ? AppTextStyles.size16Regular
                  : AppTextStyles.size16RegularTextSecondary,
              maxLines: 1,
              softWrap: false,
            ),
            const Spacer(),
            const IaSvgIcon(iconPath: AppAssets.caretRightSM, iconColor: AppColors.mediumGrey),
          ],
        ),
      );
    });
  }
}

class _IventTags extends GetView<IventCreateFormController> {
  const _IventTags();

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return IaRoundedContainer(
        onTap: () => Get.toNamed('/tags'), // Replace with proper navigation
        margin: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
        padding: const EdgeInsets.all(AppDimensions.padding20),
        roundness: AppDimensions.radiusS,
        color: AppColors.lightGrey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'Etkinliğinizi ilgili kullanıcılara ulaştırmak için:',
                  style: AppTextStyles.size16RegularTextSecondary,
                  maxLines: 1,
                  softWrap: false,
                ),
                const Spacer(),
                const IaSvgIcon(iconPath: AppAssets.caretRightSM, iconColor: AppColors.mediumGrey),
              ],
            ),
            if (controller.selectedTags.isNotEmpty) const SizedBox(height: AppDimensions.padding20),
            Wrap(
              spacing: AppDimensions.padding8,
              runSpacing: AppDimensions.padding8,
              alignment: WrapAlignment.start,
              children: controller.selectedTags
                  .map((tag) => IventCreateButtons.iventCreateTag(
                        onTap: () => controller.toggleTag(tag),
                        isSelected: controller.selectedTagIds.contains(tag.hobbyId),
                        text: tag.hobbyName,
                      ))
                  .toList(),
            ),
          ],
        ),
      );
    });
  }
}
