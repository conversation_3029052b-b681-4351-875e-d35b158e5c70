import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_floating_action_button.dart';
import 'package:ivent_app/core/widgets/foundation/containers/ia_rounded_container.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_form_controller.dart'; 

class Description extends GetView<IventCreateFormController> {
  const Description({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return IaScaffold.noSearch(
        // child: IventNameTopBarChild(controller: controller),
        showDivider: false,
        body: const _DescriptionBody(),
      );
    });
  }
}

class _DescriptionBody extends GetView<IventCreateFormController> {
  const _DescriptionBody();

  @override
  Widget build(BuildContext context) {
    final textController = TextEditingController(text: controller.description.value);

    return Column(
      children: [
        const SizedBox(height: AppDimensions.padding20),
        Expanded(
          child: IaRoundedContainer(
            margin: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
            roundness: AppDimensions.radiusL,
            color: AppColors.lightGrey,
            child: TextField(
              onChanged: (value) => controller.description.value = value,
              controller: textController,
              maxLines: null,
              style: AppTextStyles.size16Medium,
              decoration: InputDecoration(
                hintText: 'Etkinlik açıklamanızı bu alana yazabilirsiniz.',
                hintStyle: AppTextStyles.size16Regular,
                border: InputBorder.none,
                contentPadding: const EdgeInsets.all(AppDimensions.padding20),
              ),
            ),
          ),
        ),
        Obx(() {
          return IaFloatingActionButton(
            onPressed: () {
              controller.description.value = textController.text;
              Get.back();
            },
            text: 'Açıklamayı Kaydet',
            isEnabled: controller.description.value != null && controller.description.value!.isNotEmpty,
          );
        }),
      ],
    );
  }
}
