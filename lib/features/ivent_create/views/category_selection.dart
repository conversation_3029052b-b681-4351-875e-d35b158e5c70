import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/ivent_create/constants/strings.dart';
import 'package:ivent_app/features/ivent_create/widgets/common/ivent_create_category_list.dart';

class CategorySelection extends StatelessWidget {
  const CategorySelection({super.key});

  @override
  Widget build(BuildContext context) {
    return IaScaffold.noSearch(
      title: IventCreateStrings.kategoriSec,
      showDivider: false,
      bodyPadding: const EdgeInsets.symmetric(horizontal: AppDimensions.padding20),
      body: const IventCreateCategoryList(categoriesLevel: 1),
    );
  }
}
