import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/widgets/composite/tiles/ia_list_tile.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_floating_action_button.dart';
import 'package:ivent_app/core/widgets/layout/scaffolds/ia_scaffold.dart';
import 'package:ivent_app/features/ivent_create/constants/strings.dart';
import 'package:ivent_app/features/ivent_create/controllers/ivent_create_date_controller.dart';
import 'package:ivent_app/features/ivent_create/ivent_create_pages.dart';
import 'package:ivent_app/features/ivent_create/widgets/date_selection/date_add_button.dart';
import 'package:ivent_app/features/ivent_create/widgets/date_selection/date_list_tile.dart';
import 'package:ivent_app/features/ivent_create/widgets/date_selection/header.dart';

class DateSelection extends GetView<IventCreateDateController> {
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return IaScaffold.noSearch(
        child: const Header(),
        showDivider: false,
        body: const _DateList(),
        floatingActionButton: IaFloatingActionButton(
          onPressed: () => Get.toNamed(IventCreatePages.mapSelection),
          text: 'Devam Et',
          isPrimary: false,
          isEnabled: controller.canContinue,
        ),
      );
    });
  }
}

class _DateList extends GetView<IventCreateDateController> {
  const _DateList();

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return ListView.separated(
        padding: const EdgeInsets.only(top: AppDimensions.padding20, bottom: 100),
        itemCount: controller.dates.length + 1,
        itemBuilder: (context, index) {
          if (index == 0 && controller.dates.isEmpty)
            return const DateListTile(
              iconPath: AppAssets.calendar,
              infoText: IventCreateStrings.tarihSaat,
            );

          if (index == controller.dates.length) return const DateAddButton();

          return DateListTile(
            onTrailingTap: () => controller.handleDateRemoved(index),
            iconPath: index == 0 ? AppAssets.calendar : AppAssets.closeSM,
            infoText: controller.dates.length == 1 ? 'Tarih & Saat' : '${index + 1}. Gün',
            date: controller.dates[index],
          );
        },
        separatorBuilder: IaListTile.separatorBuilder20,
      );
    });
  }
}
