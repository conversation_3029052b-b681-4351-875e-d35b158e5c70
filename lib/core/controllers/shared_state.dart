import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:get/get.dart';

class FeatureSharedState extends GetxController {
  final _loadingStates = <String, bool>{}.obs;

  bool isLoading([String? tag]) {
    if (tag == null) return _loadingStates.values.any((loading) => loading);
    return _loadingStates[tag] ?? false;
  }

  void setLoading(bool value, [String? tag]) => _loadingStates[tag ?? 'default'] = value;
  void setLoadingSafe(bool value, [String? tag]) {
    if (WidgetsBinding.instance.schedulerPhase == SchedulerPhase.persistentCallbacks) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        setLoading(value, tag);
      });
    } else {
      setLoading(value, tag);
    }
  }
}

class GlobalSharedState extends GetxController {
  final _iventFavoriteUpdates = <String, bool>{}.obs;

  void updateIventFavorite(String iventId, bool isFavorited) => _iventFavoriteUpdates[iventId] = isFavorited;

  bool? getIventFavoriteUpdate(String iventId) => _iventFavoriteUpdates[iventId];
}
