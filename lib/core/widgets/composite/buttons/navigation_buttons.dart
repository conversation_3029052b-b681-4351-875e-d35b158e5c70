import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_icon_button.dart';

class NavigationButtons {
  NavigationButtons._();

  static IaIconButton navigationBar({
    Key? key,
    required VoidCallback onTap,
    required String iconPath,
    required bool isActive,
  }) {
    return IaIconButton(
      key: key,
      onPressed: onTap,
      backgroundColor: AppColors.white,
      iconPath: iconPath,
      iconColor: isActive ? AppColors.primary : AppColors.grey600,
    );
  }
}
