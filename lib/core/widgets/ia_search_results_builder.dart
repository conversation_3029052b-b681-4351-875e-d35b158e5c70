import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/foundation/graphics/ia_svg_icon.dart';
import 'package:ivent_app/core/widgets/foundation/indicators/ia_loading_indicator.dart';

enum InitialSearchBehavior {
  loaded,
  mustSearch,
}

class IaSearchResultsBuilder extends StatelessWidget {
  final String entityName;
  final String? iconPath;
  final bool isSearching;
  final bool isQueryEmpty;
  final bool isResultsEmpty;
  final InitialSearchBehavior initialSearchBehavior;
  final Widget Function(BuildContext context) builder;

  const IaSearchResultsBuilder({
    super.key,
    required this.entityName,
    this.iconPath,
    required this.isSearching,
    required this.isQueryEmpty,
    required this.isResultsEmpty,
    required this.initialSearchBehavior,
    required this.builder,
  });

  @override
  Widget build(BuildContext context) {
    return _buildBody(context);
  }

  Widget _buildBody(BuildContext context) {
    if (isSearching) return const IaLoadingIndicator();
    if (!isResultsEmpty) return builder(context);
    if (!isQueryEmpty) return _buildNoSearchedResultsText();
    return initialSearchBehavior == InitialSearchBehavior.loaded
        ? _buildNoDefaultResultsText()
        : _buildStartSearchingText();
  }

  Widget _buildNoSearchedResultsText() {
    return Center(
      child: Text(
        '${entityName} bulunamadı',
        style: AppTextStyles.size16RegularTextSecondary,
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildNoDefaultResultsText() {
    return Center(
      child: Text(
        '${entityName} bulunmamakta',
        style: AppTextStyles.size16RegularTextSecondary,
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildStartSearchingText() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (iconPath != null)
            IaSvgIcon(
              iconPath: iconPath!,
              iconSize: 50,
              iconColor: AppColors.mediumGrey,
            ),
          if (iconPath != null) const SizedBox(height: AppDimensions.padding16),
          Text(
            '${entityName} aramak için yazmaya başlayın',
            style: AppTextStyles.size16RegularTextSecondary,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
