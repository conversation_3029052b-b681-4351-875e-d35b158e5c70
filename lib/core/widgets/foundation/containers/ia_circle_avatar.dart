import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_assets.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/widgets/foundation/graphics/ia_svg_icon.dart';

class IaCircleAvatar extends StatelessWidget {
  final String? imageUrl;
  final String? iconPath;
  final Color? backgroundColor;
  final Color? iconColor;
  final double radius;

  const IaCircleAvatar({
    super.key,
    this.imageUrl,
    this.iconPath,
    this.backgroundColor,
    this.iconColor,
    required this.radius,
  });

  String get _iconPath => iconPath ?? AppAssets.image01;
  Color get _backgroundColor => backgroundColor ?? AppColors.lightGrey;
  Color get _iconColor => iconColor ?? AppColors.darkGrey;

  @override
  Widget build(BuildContext context) {
    return CircleAvatar(
      radius: radius,
      backgroundColor: _backgroundColor,
      backgroundImage: imageUrl != null ? NetworkImage(imageUrl!) : null,
      child: imageUrl == null ? IaSvgIcon(iconPath: _iconPath, iconColor: _iconColor) : null,
    );
  }
}
