import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_colors.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/widgets/foundation/buttons/ia_circular_button.dart';

class IaCheckbox extends StatelessWidget {
  final bool isSelected;
  final VoidCallback? onTap;

  const IaCheckbox({
    super.key,
    required this.isSelected,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return IaCircularButton(
      key: key,
      buttonSize: AppDimensions.buttonSizeCheckBox,
      backgroundColor: isSelected ? AppColors.primary : AppColors.lightGrey,
      border: Border.all(color: AppColors.mediumGrey, width: 1),
      onPressed: onTap,
    );
  }
}
