import 'package:flutter/material.dart';
import 'package:ivent_app/core/widgets/foundation/indicators/ia_loading_indicator.dart';

class IaBuilder extends StatelessWidget {
  final Widget Function(BuildContext context) builder;
  final Widget Function(BuildContext context)? errorBuilder;
  final Widget Function(BuildContext context)? loadingBuilder;
  final Widget Function(BuildContext context)? emptyBuilder;
  final bool isLoading;
  final bool isEmpty;
  final Exception? error;

  const IaBuilder({
    super.key,
    required this.builder,
    this.errorBuilder,
    this.loadingBuilder,
    this.emptyBuilder,
    this.isLoading = false,
    this.isEmpty = false,
    this.error,
  });

  @override
  Widget build(BuildContext context) {
    return isLoading
        ? loadingBuilder?.call(context) ?? const IaLoadingIndicator(showLoadingText: false)
        : error != null
            ? errorBuilder?.call(context) ?? const Center(child: Text('Hata'))
            : isEmpty
                ? emptyBuilder?.call(context) ?? const Center(child: Text('Boş'))
                : builder(context);
  }
}
