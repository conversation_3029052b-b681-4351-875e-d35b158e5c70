import 'package:flutter/material.dart';
import 'package:ivent_app/core/constants/app_dimensions.dart';
import 'package:ivent_app/core/constants/app_text_styles.dart';
import 'package:ivent_app/core/widgets/composite/buttons/shared_buttons.dart';
import 'package:ivent_app/core/widgets/foundation/graphics/ia_divider.dart';

class IaAppBar extends StatelessWidget {
  final AlignmentGeometry alignment;
  final double? height;
  final String? text;
  final Widget? child;
  final Widget? leading;
  final Widget? trailing;
  final bool divider;

  const IaAppBar._({
    super.key,
    this.alignment = Alignment.centerLeft,
    this.height,
    this.text,
    this.child,
    this.leading,
    this.trailing,
    this.divider = true,
  });

  static const EdgeInsets _defaultMargin =
      const EdgeInsets.fromLTRB(AppDimensions.padding20, AppDimensions.padding20, AppDimensions.padding20, 0);
  static const EdgeInsets _defaultDividerMargin = const EdgeInsets.symmetric(horizontal: AppDimensions.padding20);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          margin: _defaultMargin,
          height: height ?? AppDimensions.topBarHeight,
          width: double.maxFinite,
          child: alignment != Alignment.center ? _buildAlignedRow() : _buildCenteredRow(),
        ),
        if (divider) const IaDivider(margin: _defaultDividerMargin),
      ],
    );
  }

  Row _buildAlignedRow() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        if (leading != null) _buildAlignedLeading(),
        Expanded(child: _buildAlignedChild()),
        if (trailing != null) _buildAlignedTrailing(),
      ],
    );
  }

  Padding _buildAlignedLeading() =>
      Padding(padding: const EdgeInsets.only(right: AppDimensions.padding8), child: leading!);
  Padding _buildAlignedTrailing() =>
      Padding(padding: const EdgeInsets.only(left: AppDimensions.padding8), child: trailing!);
  Align _buildAlignedChild() => Align(alignment: alignment, child: _buildChildWidget());

  Stack _buildCenteredRow() {
    return Stack(
      children: [
        _buildCenteredChild(),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            if (leading != null) _buildCenteredLeading(),
            if (trailing != null) _buildCenteredTrailing(),
          ],
        ),
      ],
    );
  }

  Align _buildCenteredLeading() => Align(alignment: Alignment.centerLeft, child: leading!);
  Align _buildCenteredTrailing() => Align(alignment: Alignment.centerRight, child: trailing!);
  Center _buildCenteredChild() => Center(child: _buildChildWidget());

  Widget _buildChildWidget() => child ?? Text(text ?? '', style: AppTextStyles.size32Bold);

  static IaAppBar aligned({
    Key? key,
    String? text,
    Widget? child,
    Widget? trailing,
    double? height,
    bool backButton = true,
    bool divider = true,
    Color? backButtonColor,
  }) {
    return IaAppBar._(
      key: key,
      alignment: Alignment.centerLeft,
      text: text,
      child: child,
      leading: backButton ? SharedButtons.backButton(color: backButtonColor) : null,
      trailing: trailing,
      divider: divider,
      height: height,
    );
  }

  static IaAppBar centered({
    Key? key,
    Widget? leading,
    Widget? trailing,
    bool divider = true,
    double? height,
    required String title,
    required String subtitle,
  }) {
    return IaAppBar._(
      key: key,
      alignment: Alignment.center,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(title, style: AppTextStyles.size16Bold),
          Text('@${subtitle}', style: AppTextStyles.size12MediumTextSecondary),
        ],
      ),
      leading: leading,
      trailing: trailing,
      divider: divider,
      height: height,
    );
  }
}
