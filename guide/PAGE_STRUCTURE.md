## Page Structure

### StatelessWidget with <PERSON><PERSON>iew Pattern

Use `StatelessWidget` with `GetView<T>` for pages that need controller access:

```dart
class UserProfile extends StatelessWidget {
  final String userId;

  const UserProfile({Key? key, required this.userId}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return IaScaffold.empty(
      body: Column(
        children: [
          const ProfileHeader(),
          DefaultTabController(
            length: 2,
            child: NestedScrollView(
              headerSliverBuilder: (context, value) {
                return [
                  const SliverToBoxAdapter(child: ProfileInfoSection()),
                ];
              },
              body: const ProfileTabs(),
            ),
          ),
        ],
      ),
    );
  }
}
```

### Widget Components with GetView

Use `GetView<ControllerType>` for widgets that need specific controller access:

```dart
class ProfileInfoSection extends GetView<ProfileUserInfoController> {
  final String userId;

  const ProfileInfoSection({super.key, required this.userId});

  @override
  String? get tag => userId; // Use tag for multi-instance controllers

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.isLoading('loadUserInfo')) {
        return const IaLoadingIndicator();
      }

      return Column(
        children: [
          Text(controller.userPageInfo?.name ?? ''),
          IaRoundedButton(
            text: 'Follow',
            onPressed: controller.toggleFollowing,
          ),
        ],
      );
    });
  }
}
```

### IaScaffold Variants

Choose the appropriate `IaScaffold` variant based on your page needs:

```dart
// For pages with custom layout and no standard app bar
IaScaffold.empty(
  body: CustomLayoutWidget(),
)

// For pages with standard app bar and navigation
IaScaffold.basic(
  appBar: IaTopBar(
    title: 'Page Title',
  ),
  body: PageContent(),
)
```

### Widget Composition Patterns

Break down complex pages into smaller, focused widgets:

```dart
class ComplexPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return IaScaffold.empty(
      body: Column(
        children: [
          const PageHeader(),        // Separate header widget
          const PageContent(),       // Main content widget
          const PageActions(),       // Action buttons widget
        ],
      ),
    );
  }
}

// Private widgets for internal page structure
class _PageSection extends GetView<SpecificController> {
  const _PageSection();

  @override
  Widget build(BuildContext context) {
    return Obx(() =>
      controller.isLoading()
        ? const IaLoadingIndicator()
        : ActualContent()
    );
  }
}
```

### Page Structure Rules

- **Always use `IaScaffold`** instead of `Scaffold` for consistent styling
- Use `StatelessWidget` for most pages - avoid `StatefulWidget` unless absolutely necessary
- Use `GetView<ControllerType>` for widgets that need specific controller access
- Use `Obx()` for reactive UI updates based on controller state
- Prefer Ia-prefixed widgets over standard Flutter widgets for consistency
- Handle loading states with `IaLoadingIndicator` and `controller.isLoading(tag)`
- Break complex pages into smaller, focused widget components
- Use private widgets (prefixed with `_`) for internal page structure
- Pass required data through constructor parameters, not through controllers when possible
- Use `tag` property in `GetView` when working with tagged controllers (multi-instance features)
