## Validation Patterns

### Form Validation

```dart
class ValidationController extends BaseController<FeatureSharedState> {
  bool get isField1Valid => state.field1.length >= ValidationConstants.minLength;
  bool get isField2Valid => state.field2.length >= ValidationConstants.minLength;
  bool get isFormValid => isField1Valid && isField2Valid;
}
```

### Validation Rules

- Create validation constants for reusable rules
- Implement validation in controllers, not widgets
- Provide real-time validation feedback
- Use descriptive validation error messages
