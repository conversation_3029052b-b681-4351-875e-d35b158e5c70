## Search Functionality Patterns

### Search Controller Extension

For features with search functionality:

```dart
class SearchController extends BaseControllerWithSearch<FeatureSharedState> {
  final _searchResults = <ResultType>[].obs;

  SearchController(AuthService authService, FeatureSharedState state) : super(authService, state);

  List<ResultType> get searchResults => _searchResults;

  @override
  bool get isResultsEmpty => _searchResults.isEmpty;

  @override
  InitialSearchBehavior get initialSearchBehavior => InitialSearchBehavior.loaded;

  @override
  Future<void> onSearch([String? query]) async {
    // No need for runSafe as it's handled by BaseControllerWithSearch
    // This is the only exception
    final results = await api.search(query);
    _searchResults.assignAll(results ?? []);
  }
}
```
