## Code Quality Standards

### General Rules

1. **Single Responsibility**: Each class has one clear purpose
2. **Dependency Injection**: Use constructor injection for dependencies
3. **Immutability**: Prefer immutable classes and final fields
4. **Error Handling**: Handle errors gracefully with user feedback
5. **Performance**: Use lazy loading and efficient state management
6. **Testing**: Write testable code with clear separation of concerns

### Code Review Checklist

- [ ] Follows naming conventions
- [ ] Proper directory structure
- [ ] Extends appropriate base classes
- [ ] Handles errors gracefully
- [ ] Uses reactive state management correctly
- [ ] Do not include any documentation
- [ ] Write self-explanatory code
- [ ] Follows import organization
- [ ] Implements proper resource cleanup
