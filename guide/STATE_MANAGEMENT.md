## State Management

### Shared State Pattern

Each feature has a dedicated shared state class.  
This shared class helps manage the state that needs to be accessible across multiple controllers within the feature:

```dart
class FeatureSharedState extends SharedState {
  // Reactive variables using GetX observables
  final _property = ''.obs;
  final _list = <Type>[].obs;
  final _nullable = Rxn<Type>();

  // Getters and setters
  Type get property => _property.value;
  set property(Type value) => _property.value = value;

  // Helper methods for state manipulation
  void clearAll() {
    // Reset state to defaults
  }
}
```

### State Management Rules

- Use GetX observables (`.obs`, `Rxn<T>()`) for reactive state
- Provide getters and setters for clean access
- Include helper methods for complex state operations
- Keep state classes focused on data, not business logic
