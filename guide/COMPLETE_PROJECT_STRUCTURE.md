## Complete Project Structure

### Root Directory Organization

```
lib/
├── api/                    # AUTO-GENERATED - OpenAPI/Swagger codegen
├── core/                   # App-wide shared components and utilities
├── features/               # Feature-based modules
├── routes/                 # Navigation and route definitions
├── shared/                 # Cross-feature shared code
└── main.dart              # Application entry point
```

### API Layer (Auto-Generated)

**⚠️ CRITICAL: The `lib/api/` directory is auto-generated by OpenAPI/Swagger codegen and must NEVER be manually modified.**

```
lib/api/
├── api/                   # Generated API client classes
├── auth/                  # Generated authentication classes
├── doc/                   # Generated API documentation
├── model/                 # Generated data models and DTOs
├── api.dart              # Main API export file
├── api_client.dart       # HTTP client implementation
├── api_exception.dart    # API exception handling
└── api_helper.dart       # API utility functions
```

**API Integration Rules:**

- Never edit files in `lib/api/` directly
- Use the generated API classes through the getters inside `BaseController` layer

### Core Directory Structure

```
lib/core/
├── cache/                 # Caching mechanisms
├── constants/             # App-wide constants (colors, dimensions, styles)
├── error/                 # Error handling utilities
├── services/              # Core services (AuthService, etc.)
├── utils/                 # Utility functions and extensions
└── widgets/               # Reusable UI components with "Ia" prefix
    ├── composite/         # Complex composed widgets
    │   ├── buttons/       # Composite button components
    │   ├── feedback/      # User feedback components
    │   └── tiles/         # List and card tiles
    ├── foundation/        # Basic UI building blocks
    │   ├── buttons/       # Basic button components
    │   ├── containers/    # Container widgets
    │   ├── dialogs/       # Dialog components
    │   ├── graphics/      # Icons, dividers, graphics
    │   ├── indicators/    # Loading, search placeholders
    │   └── inputs/        # Input fields and controls
    └── layout/            # Layout and structural components
        ├── navigation/    # Navigation bars and components
        ├── panels/        # Panel and overlay components
        ├── scaffolds/     # Custom scaffold implementations
        └── screens/       # Screen layout templates
```

### Shared Directory Structure

```
lib/shared/
├── constants/             # Cross-feature constants
├── controllers/           # Base controllers and shared state
├── domain/                # Cross-feature domain entities
│   └── entities/          # Shared business entities
├── views/                 # Shared pages (error screens, etc.)
└── widgets/               # Shared widgets
```

### Routes Directory Structure

```
lib/routes/
├── app_pages.dart        # Main route configuration
├── global_bindings.dart  # Global dependency bindings
└── other.dart           # Utility and error routes
```
